<template>
  <div class="article-page">
    <n-spin :show="loading">
      <template v-if="article && article._id">
        <!-- 文章主体 -->
        <div class="article-container">
          <div class="article-main">
            <n-card :bordered="false">
              <!-- 文章标题 -->
              <h1 class="article-title">{{ article.title }}</h1>

              <!-- 文章元信息 -->
              <div class="article-meta">
                <n-space align="center">
                  <n-avatar round :size="32" :src="article.author?.avatar">
                    {{ article.author?.username?.charAt(0)?.toUpperCase() }}
                  </n-avatar>
                  <span class="author-name">{{ article.author?.username }}</span>
                  <n-divider vertical />
                  <n-space>
                    <n-tag :bordered="false" class="category-tag">
                      {{ article.category?.name }}
                    </n-tag>
                    <n-tag
                      v-for="tag in article.tags"
                      :key="tag._id"
                      :bordered="false"
                      type="info"
                      class="article-tag"
                    >
                      {{ tag.name }}
                    </n-tag>
                  </n-space>
                  <n-divider vertical />
                  <span>{{ formatDate(article.created_at) }}</span>
                  <n-divider vertical />
                  <n-space align="center">
                    <n-icon><Eye /></n-icon>
                    {{ article.view_count }}
                  </n-space>
                </n-space>
              </div>

              <!-- 文章封面 -->
              <div v-if="article.cover_image" class="article-cover">
                <img :src="article.cover_image" :alt="article.title">
              </div>

              <!-- 文章内容 -->
              <div class="article-content">
                <div v-html="article.content" class="rich-content"></div>
              </div>
            </n-card>
          </div>

          <!-- 侧边栏 -->
          <div class="article-sidebar">
            <!-- 作者信息 -->
            <n-card :bordered="false" class="author-card">
              <n-space vertical :size="16">
                <div class="author-header">
                  <n-avatar
                    round
                    :size="64"
                    :src="article.author?.avatar"
                  >
                    {{ article.author?.username?.charAt(0)?.toUpperCase() }}
                  </n-avatar>
                  <div class="author-info">
                    <h3>{{ article.author?.username }}</h3>
                    <p>{{ article.author?.bio || '这个作者很懒，什么都没写~' }}</p>
                  </div>
                </div>
              </n-space>
            </n-card>

            <!-- 相关文章 -->
            <n-card v-if="relatedArticles.length > 0" title="相关文章" :bordered="false" class="related-articles">
              <n-list>
                <n-list-item v-for="item in relatedArticles" :key="item._id">
                  <router-link :to="`/articles/${item._id}`" class="related-article-item">
                    <n-ellipsis :line-clamp="2">{{ item.title }}</n-ellipsis>
                  </router-link>
                </n-list-item>
              </n-list>
            </n-card>
          </div>
        </div>
      </template>
      <n-empty v-else-if="!loading" description="文章不存在或已被删除" />
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Eye } from '@vicons/ionicons5'
import { articleApi } from '@/api'
import type { Article } from '@/types/api'
import { formatDate } from '@/utils/format'
import { useMessage } from 'naive-ui'
import {
  NCard,
  NSpin,
  NSpace,
  NTag,
  NAvatar,
  NIcon,
  NList,
  NListItem,
  NEmpty,
  NDivider,
  NEllipsis
} from 'naive-ui'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const article = ref<Article | null>(null)
const relatedArticles = ref<Article[]>([])
const loading = ref(true)

const fetchArticle = async () => {
  try {
    loading.value = true
    const res = await articleApi.getArticle(route.params.id as string)
    if (res.data?.code === 0 || res.data?.code === 200) {
      article.value = res.data.data
      
      // 文章浏览计数
      setTimeout(() => {
        articleApi.incrementViewCount(article.value?.id).catch(error => {
          console.error('增加文章浏览量失败:', error)
        })
      }, 5000)
      
      // 获取相关文章
      if (article.value?.tags && article.value.tags.length > 0) {
        const relatedRes = await articleApi.getArticles({
          tag: article.value.tags[0].id,
          limit: 5
        })
        if (relatedRes.data?.code === 0 || relatedRes.data?.code === 200) {
          relatedArticles.value = relatedRes.data.data.items.filter(
            item => item.id !== article.value?.id
          )
        }
      }
    } else {
      message.error('文章不存在或已被删除')
      router.push('/articles')
    }
  } catch (error: any) {
    console.error('获取文章详情失败:', error)
    message.error(error.message || '获取文章失败')
    router.push('/articles')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  const id = route.params.id
  if (id && id !== 'undefined') {
    fetchArticle()
  } else {
    message.error('无效的文章ID')
    router.push('/articles')
  }
})
</script>

<style lang="scss" scoped>
.article-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 2rem 0;
}

.article-container {
  display: grid;
  grid-template-columns: minmax(0, 1fr) 300px;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.article-main {
  .n-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .article-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 1.5rem;
    color: #2c3e50;
    line-height: 1.3;
    letter-spacing: -0.02em;
  }

  .article-meta {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;

    :deep(.category-tag) {
      background: linear-gradient(120deg, #00B4DB, #0083B0);
      color: white;
      font-weight: 500;
      padding: 4px 12px;
      border-radius: 20px;
      margin-right: 1rem;
      border: none;
    }

    :deep(.article-tag) {
      background-color: #f0f7ff;
      color: #0083B0;
      border: 1px solid #e1f0ff;
      padding: 2px 10px;
      border-radius: 4px;
      font-size: 0.9rem;
      transition: all 0.3s ease;

      &:hover {
        background-color: #e1f0ff;
        border-color: #0083B0;
      }
    }

    .author-name {
      font-weight: 500;
      color: #2c3e50;
      margin: 0 0.5rem;
    }
  }

  .article-cover {
    margin: -1.5rem -1.5rem 2rem;
    border-radius: 16px 16px 0 0;
    overflow: hidden;

    img {
      width: 100%;
      height: auto;
      max-height: 500px;
      object-fit: cover;
      display: block;
    }
  }

  .article-content {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    
    :deep(.rich-content) {
      font-size: 1.1rem;
      line-height: 1.8;
      
      img {
        max-width: 100%;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
      }
      
      video {
        max-width: 100%;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        background-color: #000;
      }
      
      h1, h2, h3, h4, h5, h6 {
        margin: 1.5rem 0 1rem;
        color: #2c3e50;
        font-weight: 600;
      }
      
      a {
        color: #0083B0;
        text-decoration: none;
        border-bottom: 1px solid #0083B0;
        transition: all 0.3s ease;
        
        &:hover {
          color: #00B4DB;
          border-color: #00B4DB;
        }
      }
      
      blockquote {
        border-left: 4px solid #0083B0;
        padding: 0.5rem 1rem;
        margin: 1rem 0;
        background-color: #f0f7ff;
        color: #2c3e50;
        font-style: italic;
      }
      
      code {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
        padding: 0.2em 0.4em;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
        font-size: 85%;
        color: #476582;
      }
      
      pre {
        background-color: #282c34;
        color: #abb2bf;
        border-radius: 6px;
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
        font-size: 14px;
        line-height: 1.5;
        box-shadow: none;
        
        code {
          background-color: transparent;
          padding: 0;
          color: inherit;
          font-family: inherit;
          font-size: inherit;
          line-height: inherit;
        }
      }
      
      table {
        border-collapse: collapse;
        width: 100%;
        margin: 1rem 0;
        
        th, td {
          border: 1px solid #ddd;
          padding: 0.5rem;
        }
        
        th {
          background-color: #f5f7fa;
          font-weight: 600;
        }
        
        tr:nth-child(even) {
          background-color: #f9f9f9;
        }
      }
      
      ul, ol {
        margin: 1rem 0;
        padding-left: 2rem;
        
        li {
          margin-bottom: 0.5rem;
        }
      }
    }
  }
}

.article-sidebar {
  .author-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;

    .author-header {
      display: flex;
      gap: 1rem;
      align-items: center;

      h3 {
        margin: 0 0 0.5rem;
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
      }

      p {
        margin: 0;
        color: #64748b;
        font-size: 0.95rem;
        line-height: 1.5;
      }
    }
  }

  .related-articles {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    :deep(.n-card-header) {
      font-size: 1.2rem;
      font-weight: 600;
      color: #2c3e50;
    }

    .related-article-item {
      display: block;
      color: #2c3e50;
      text-decoration: none;
      padding: 0.75rem 0;
      transition: color 0.2s ease;

      &:hover {
        color: #00B4DB;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .article-container {
    grid-template-columns: 1fr;
    padding: 0 1.5rem;
  }

  .article-main {
    .article-title {
      font-size: 2rem;
    }
  }
}

@media (max-width: 768px) {
  .article-page {
    padding: 1rem 0;
  }

  .article-container {
    padding: 0 1rem;
  }

  .article-main {
    .article-title {
      font-size: 1.75rem;
    }

    .article-meta {
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .article-content {
      font-size: 1rem;

      :deep(h1) {
        font-size: 1.75rem;
      }

      :deep(h2) {
        font-size: 1.5rem;
      }

      :deep(h3) {
        font-size: 1.25rem;
      }
    }
  }
}
</style> 