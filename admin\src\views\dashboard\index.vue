<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="dashboard">
    <a-row :gutter="[16, 16]">
      <!-- 统计卡片 -->
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card" :bordered="false" :body-style="{ padding: '20px' }">
          <div class="stat-content">
            <div class="stat-icon">
              <FileTextOutlined />
            </div>
            <div class="stat-info">
              <div class="stat-title">文章总数</div>
              <div class="stat-number">{{ stats.articles.total }}</div>
              <div class="stat-desc">
                已发布: {{ stats.articles.published }} | 草稿: {{ stats.articles.draft }}
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card" :bordered="false" :body-style="{ padding: '20px' }">
          <div class="stat-content">
            <div class="stat-icon user-icon">
              <TeamOutlined />
            </div>
            <div class="stat-info">
              <div class="stat-title">用户总数</div>
              <div class="stat-number">{{ stats.users.total }}</div>
              <div class="stat-desc">活跃用户: {{ stats.users.active }}</div>
            </div>
          </div>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card" :bordered="false" :body-style="{ padding: '20px' }">
          <div class="stat-content">
            <div class="stat-icon category-icon">
              <AppstoreOutlined />
            </div>
            <div class="stat-info">
              <div class="stat-title">分类总数</div>
              <div class="stat-number">{{ stats.categories.total }}</div>
              <div class="stat-desc">内容分类管理</div>
            </div>
          </div>
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card" :bordered="false" :body-style="{ padding: '20px' }">
          <div class="stat-content">
            <div class="stat-icon tag-icon">
              <TagsOutlined />
            </div>
            <div class="stat-info">
              <div class="stat-title">标签总数</div>
              <div class="stat-number">{{ stats.tags.total }}</div>
              <div class="stat-desc">文章标签管理</div>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- 文章发布趋势图 -->
      <a-col :span="24">
        <a-card :bordered="false" class="chart-card">
          <template #title>
            <div class="chart-header">
              <div class="chart-title">
                <LineChartOutlined /> 文章发布趋势
              </div>
              <a-radio-group v-model:value="chartPeriod" size="small">
                <a-radio-button value="7">7天</a-radio-button>
                <a-radio-button value="30">30天</a-radio-button>
              </a-radio-group>
            </div>
          </template>
          <div ref="trendChartRef" class="trend-chart"></div>
        </a-card>
      </a-col>

      <!-- 最近文章和热门文章 -->
      <a-col :xs="24" :lg="12">
        <a-card :bordered="false" class="list-card">
          <template #title>
            <span><HistoryOutlined /> 最近文章</span>
          </template>
          <a-list :data-source="stats.recentArticles" :pagination="false">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <div class="article-title">
                      <router-link :to="'/articles/edit/' + item.id">{{ item.title }}</router-link>
                      <a-tag :color="getStatusColor(item.status)">{{ getStatusText(item.status) }}</a-tag>
                    </div>
                  </template>
                  <template #description>
                    <div class="article-meta">
                      <span><UserOutlined /> {{ item.author?.username || '未知作者' }}</span>
                      <span><FolderOutlined /> {{ item.category?.name || '未分类' }}</span>
                      <span><EyeOutlined /> {{ item.view_count || 0 }}</span>
                      <span><ClockCircleOutlined /> {{ formatDate(item.created_at) }}</span>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>

      <a-col :xs="24" :lg="12">
        <a-card :bordered="false" class="list-card">
          <template #title>
            <span><FireOutlined /> 热门文章</span>
          </template>
          <a-list :data-source="stats.popularArticles" :pagination="false">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <div class="article-title">
                      <router-link :to="'/articles/edit/' + item.id">{{ item.title }}</router-link>
                      <a-tag :color="getStatusColor(item.status)">{{ getStatusText(item.status) }}</a-tag>
                    </div>
                  </template>
                  <template #description>
                    <div class="article-meta">
                      <span><UserOutlined /> {{ item.author?.username || '未知作者' }}</span>
                      <span><FolderOutlined /> {{ item.category?.name || '未分类' }}</span>
                      <span><EyeOutlined /> {{ item.view_count || 0 }}</span>
                      <span><ClockCircleOutlined /> {{ formatDate(item.created_at) }}</span>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import {
  FileTextOutlined,
  TeamOutlined,
  AppstoreOutlined,
  TagsOutlined,
  LineChartOutlined,
  HistoryOutlined,
  FireOutlined,
  UserOutlined,
  FolderOutlined,
  EyeOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import dashboardApi from '@/api/modules/dashboard'
import type { DashboardStatsResponse } from '@/api/modules/dashboard'

const trendChartRef = ref<HTMLElement>()
let trendChart: echarts.ECharts | null = null
const chartPeriod = ref('7')

// 统计数据
const stats = ref<DashboardStatsResponse['data']>({
  articles: {
    total: 0,
    published: 0,
    draft: 0,
    archived: 0
  },
  articleTrend: [],
  users: {
    total: 0,
    active: 0
  },
  categories: {
    total: 0
  },
  tags: {
    total: 0
  },
  recentArticles: [],
  popularArticles: []
})

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 获取文章状态颜色
const getStatusColor = (status: string) => {
  const colors = {
    published: 'success',
    draft: 'warning',
    archived: 'default'
  }
  return colors[status as keyof typeof colors]
}

// 获取文章状态文本
const getStatusText = (status: string) => {
  const texts = {
    published: '已发布',
    draft: '草稿',
    archived: '已归档'
  }
  return texts[status as keyof typeof texts]
}

// 初始化趋势图
const initTrendChart = () => {
  if (trendChartRef.value && stats.value.articleTrend && stats.value.articleTrend.length > 0) {
    trendChart = echarts.init(trendChartRef.value)
    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#e6e9ec',
        borderWidth: 1,
        textStyle: {
          color: '#595959'
        },
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(0, 0, 0, 0.03)'
          }
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: stats.value.articleTrend.map(item => item.date),
        axisLine: {
          lineStyle: {
            color: '#e6e9ec'
          }
        },
        axisLabel: {
          color: '#8c8c8c'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0'
          }
        },
        axisLabel: {
          color: '#8c8c8c'
        }
      },
      series: [
        {
          name: '发布文章数',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#1890ff'
          },
          lineStyle: {
            width: 3
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(24, 144, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(24, 144, 255, 0.1)'
              }
            ])
          },
          data: stats.value.articleTrend.map(item => item.count)
        }
      ]
    }
    trendChart.setOption(option)
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const res = await dashboardApi.getStats()
    stats.value = res.data
    initTrendChart()
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
  }
}

// 监听窗口大小变化
const handleResize = () => {
  trendChart?.resize()
}

onMounted(() => {
  fetchStats()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  trendChart?.dispose()
})
</script>

<style scoped>
.dashboard {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.stat-card {
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: #e6f7ff;
  color: #1890ff;
  font-size: 24px;
  margin-right: 16px;
}

.stat-icon.user-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.stat-icon.category-icon {
  background-color: #fff7e6;
  color: #fa8c16;
}

.stat-icon.tag-icon {
  background-color: #f9f0ff;
  color: #722ed1;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
  margin-bottom: 4px;
}

.stat-desc {
  font-size: 12px;
  color: #8c8c8c;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.trend-chart {
  height: 400px;
  margin-top: 16px;
}

.list-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.article-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.article-title a {
  color: #262626;
  font-weight: 500;
  text-decoration: none;
}

.article-title a:hover {
  color: #1890ff;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #8c8c8c;
  font-size: 12px;
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  min-height: 48px;
}

:deep(.ant-list-item) {
  padding: 12px 0;
}

:deep(.ant-list-item-meta-title) {
  margin-bottom: 8px;
}
</style>
