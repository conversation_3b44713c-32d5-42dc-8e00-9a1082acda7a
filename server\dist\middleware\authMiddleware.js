"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.restrictTo = exports.protect = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const catchAsync_1 = require("../utils/catchAsync");
const appError_1 = __importDefault(require("../utils/appError"));
const User_1 = __importDefault(require("../models/User"));
exports.protect = (0, catchAsync_1.catchAsync)(async (req, res, next) => {
    // 1) 获取token
    let token;
    if (req.headers.authorization?.startsWith('Bearer')) {
        token = req.headers.authorization.split(' ')[1];
        console.log('收到认证请求，Token:', token.substring(0, 10) + '...');
    }
    else if (req.cookies?.token) {
        token = req.cookies.token;
        console.log('从Cookie获取Token');
    }
    else if (req.query?.token) {
        token = req.query.token;
        console.log('从URL查询参数获取Token');
    }
    if (!token) {
        console.log('未提供Token，拒绝访问');
        return next(new appError_1.default('您未登录！请先登录', 401));
    }
    try {
        // 2) 验证token
        console.log('正在验证Token，使用密钥:', process.env.JWT_SECRET?.substring(0, 3) + '...');
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'blogadminsecretkey2024');
        console.log('Token验证成功，解码信息:', decoded);
        // 3) 检查用户是否仍然存在
        const currentUser = await User_1.default.findByPk(decoded.id);
        if (!currentUser) {
            console.log('用户不存在:', decoded.id);
            return next(new appError_1.default('此token所属的用户不存在', 401));
        }
        console.log('用户验证成功:', currentUser.username, '角色:', currentUser.role);
        // 4) 将用户信息添加到请求对象
        req.user = currentUser;
        next();
    }
    catch (error) {
        console.error('Token验证失败:', error);
        return next(new appError_1.default('无效的token或token已过期，请重新登录', 401));
    }
});
const restrictTo = (...roles) => {
    return (req, res, next) => {
        if (!roles.includes(req.user.role)) {
            return next(new appError_1.default('您没有权限执行此操作', 403));
        }
        next();
    };
};
exports.restrictTo = restrictTo;
