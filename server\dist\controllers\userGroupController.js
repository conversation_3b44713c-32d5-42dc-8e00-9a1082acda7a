"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserGroups = exports.removeMemberFromGroup = exports.addMemberToGroup = exports.deleteUserGroup = exports.updateUserGroup = exports.createUserGroup = exports.getUserGroup = exports.getAllUserGroups = void 0;
const sequelize_1 = require("sequelize");
const UserGroup_1 = __importDefault(require("../models/UserGroup"));
const User_1 = __importDefault(require("../models/User"));
const associations_1 = require("../models/associations");
const appError_1 = __importDefault(require("../utils/appError"));
const catchAsync_1 = require("../utils/catchAsync");
const logger_1 = __importDefault(require("../utils/logger"));
// 获取所有用户组
exports.getAllUserGroups = (0, catchAsync_1.catchAsync)(async (req, res, next) => {
    try {
        const { page = 1, limit = 10, search } = req.query;
        const pageNum = parseInt(page);
        const pageSize = parseInt(limit);
        const offset = (pageNum - 1) * pageSize;
        // 构建查询条件
        const whereClause = {};
        if (search) {
            whereClause[sequelize_1.Op.or] = [
                { name: { [sequelize_1.Op.like]: `%${search}%` } },
                { description: { [sequelize_1.Op.like]: `%${search}%` } }
            ];
        }
        const { count, rows: userGroups } = await UserGroup_1.default.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: User_1.default,
                    as: 'members',
                    attributes: ['id', 'username', 'nickname', 'email', 'avatar'],
                    through: { attributes: [] }
                }
            ],
            order: [['created_at', 'DESC']],
            offset,
            limit: pageSize
        });
        res.status(200).json({
            code: 200,
            message: '获取用户组列表成功',
            data: {
                total: count,
                items: userGroups,
                page: pageNum,
                pageSize,
                totalPages: Math.ceil(count / pageSize)
            }
        });
    }
    catch (error) {
        logger_1.default.error('获取用户组列表失败:', error);
        res.status(500).json({
            code: 500,
            message: '获取用户组列表失败',
            error: error.message
        });
    }
});
// 获取单个用户组
exports.getUserGroup = (0, catchAsync_1.catchAsync)(async (req, res, next) => {
    try {
        const { id } = req.params;
        const userGroup = await UserGroup_1.default.findByPk(id, {
            include: [
                {
                    model: User_1.default,
                    as: 'members',
                    attributes: ['id', 'username', 'nickname', 'email', 'avatar'],
                    through: { attributes: [] }
                }
            ]
        });
        if (!userGroup) {
            return next(new appError_1.default('用户组不存在', 404));
        }
        res.status(200).json({
            code: 200,
            message: '获取用户组成功',
            data: userGroup
        });
    }
    catch (error) {
        logger_1.default.error('获取用户组失败:', error);
        return next(new appError_1.default('获取用户组失败', 500));
    }
});
// 创建用户组
exports.createUserGroup = (0, catchAsync_1.catchAsync)(async (req, res, next) => {
    try {
        const { name, description, memberIds = [] } = req.body;
        // 检查用户组名是否已存在
        const existingGroup = await UserGroup_1.default.findOne({ where: { name } });
        if (existingGroup) {
            return next(new appError_1.default('用户组名称已存在', 400));
        }
        // 创建用户组
        const userGroup = await UserGroup_1.default.create({
            name,
            description
        });
        // 添加成员
        if (memberIds && memberIds.length > 0) {
            const users = await User_1.default.findAll({
                where: { id: { [sequelize_1.Op.in]: memberIds } }
            });
            await userGroup.setMembers(users);
        }
        // 获取完整的用户组信息
        const fullUserGroup = await UserGroup_1.default.findByPk(userGroup.id, {
            include: [
                {
                    model: User_1.default,
                    as: 'members',
                    attributes: ['id', 'username', 'nickname', 'email', 'avatar'],
                    through: { attributes: [] }
                }
            ]
        });
        res.status(201).json({
            code: 201,
            message: '创建用户组成功',
            data: fullUserGroup
        });
    }
    catch (error) {
        logger_1.default.error('创建用户组失败:', error);
        return next(new appError_1.default('创建用户组失败', 500));
    }
});
// 更新用户组
exports.updateUserGroup = (0, catchAsync_1.catchAsync)(async (req, res, next) => {
    try {
        const { id } = req.params;
        const { name, description, memberIds } = req.body;
        const userGroup = await UserGroup_1.default.findByPk(id);
        if (!userGroup) {
            return next(new appError_1.default('用户组不存在', 404));
        }
        // 更新基本信息
        await userGroup.update({ name, description });
        // 更新成员
        if (memberIds !== undefined) {
            const users = await User_1.default.findAll({
                where: { id: { [sequelize_1.Op.in]: memberIds } }
            });
            await userGroup.setMembers(users);
        }
        // 获取更新后的完整信息
        const updatedUserGroup = await UserGroup_1.default.findByPk(id, {
            include: [
                {
                    model: User_1.default,
                    as: 'members',
                    attributes: ['id', 'username', 'nickname', 'email', 'avatar'],
                    through: { attributes: [] }
                }
            ]
        });
        res.status(200).json({
            code: 200,
            message: '更新用户组成功',
            data: updatedUserGroup
        });
    }
    catch (error) {
        logger_1.default.error('更新用户组失败:', error);
        return next(new appError_1.default('更新用户组失败', 500));
    }
});
// 删除用户组
exports.deleteUserGroup = (0, catchAsync_1.catchAsync)(async (req, res, next) => {
    try {
        const { id } = req.params;
        const userGroup = await UserGroup_1.default.findByPk(id);
        if (!userGroup) {
            return next(new appError_1.default('用户组不存在', 404));
        }
        await userGroup.destroy();
        res.status(200).json({
            code: 200,
            message: '删除用户组成功'
        });
    }
    catch (error) {
        logger_1.default.error('删除用户组失败:', error);
        return next(new appError_1.default('删除用户组失败', 500));
    }
});
// 添加成员到用户组
exports.addMemberToGroup = (0, catchAsync_1.catchAsync)(async (req, res, next) => {
    try {
        const { groupId, userId } = req.params;
        const userGroup = await UserGroup_1.default.findByPk(groupId);
        if (!userGroup) {
            return next(new appError_1.default('用户组不存在', 404));
        }
        const user = await User_1.default.findByPk(userId);
        if (!user) {
            return next(new appError_1.default('用户不存在', 404));
        }
        // 检查用户是否已经在组中
        const existingMember = await associations_1.UserGroupMember.findOne({
            where: { user_group_id: groupId, user_id: userId }
        });
        if (existingMember) {
            return next(new appError_1.default('用户已经在该组中', 400));
        }
        await associations_1.UserGroupMember.create({
            user_group_id: parseInt(groupId),
            user_id: parseInt(userId)
        });
        res.status(200).json({
            code: 200,
            message: '添加成员成功'
        });
    }
    catch (error) {
        logger_1.default.error('添加成员失败:', error);
        return next(new appError_1.default('添加成员失败', 500));
    }
});
// 从用户组移除成员
exports.removeMemberFromGroup = (0, catchAsync_1.catchAsync)(async (req, res, next) => {
    try {
        const { groupId, userId } = req.params;
        const member = await associations_1.UserGroupMember.findOne({
            where: { user_group_id: groupId, user_id: userId }
        });
        if (!member) {
            return next(new appError_1.default('用户不在该组中', 404));
        }
        await member.destroy();
        res.status(200).json({
            code: 200,
            message: '移除成员成功'
        });
    }
    catch (error) {
        logger_1.default.error('移除成员失败:', error);
        return next(new appError_1.default('移除成员失败', 500));
    }
});
// 获取用户所属的用户组
exports.getUserGroups = (0, catchAsync_1.catchAsync)(async (req, res, next) => {
    try {
        const { userId } = req.params;
        const user = await User_1.default.findByPk(userId, {
            include: [
                {
                    model: UserGroup_1.default,
                    as: 'groups',
                    attributes: ['id', 'name', 'description'],
                    through: { attributes: [] }
                }
            ]
        });
        if (!user) {
            return next(new appError_1.default('用户不存在', 404));
        }
        res.status(200).json({
            code: 200,
            message: '获取用户组成功',
            data: user.groups || []
        });
    }
    catch (error) {
        logger_1.default.error('获取用户组失败:', error);
        return next(new appError_1.default('获取用户组失败', 500));
    }
});
