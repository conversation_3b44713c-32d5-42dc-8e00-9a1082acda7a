"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const db_1 = require("../config/db");
const User_1 = __importDefault(require("../models/User"));
const Setting_1 = __importDefault(require("../models/Setting"));
const Category_1 = __importDefault(require("../models/Category"));
const Tag_1 = __importDefault(require("../models/Tag"));
// 加载环境变量
dotenv_1.default.config();
// 创建种子数据
const seedData = async () => {
    try {
        console.log('开始创建种子数据...');
        // 连接数据库
        await db_1.sequelize.authenticate();
        await db_1.sequelize.sync({ alter: true });
        console.log('数据库连接成功');
        // 检查是否已存在管理员
        const adminExists = await User_1.default.findOne({ where: { role: 'admin' } });
        if (adminExists) {
            console.log('管理员用户已存在，跳过创建');
        }
        else {
            // 创建管理员用户
            const admin = await User_1.default.create({
                username: 'admin',
                email: '<EMAIL>',
                password: 'admin123', // 会自动加密
                role: 'admin',
                nickname: '系统管理员',
                bio: '系统默认管理员账户',
                avatar: '/uploads/touxiang.png',
                is_active: true
            });
            console.log('管理员用户创建成功:', admin.username);
        }
        // 创建默认分类
        const defaultCategories = [
            { name: '技术分享', description: '技术相关的文章分享', slug: 'tech' },
            { name: '生活随笔', description: '生活感悟和随笔', slug: 'life' },
            { name: '学习笔记', description: '学习过程中的笔记记录', slug: 'study' }
        ];
        for (const categoryData of defaultCategories) {
            const exists = await Category_1.default.findOne({ where: { slug: categoryData.slug } });
            if (!exists) {
                await Category_1.default.create(categoryData);
                console.log(`创建分类: ${categoryData.name}`);
            }
        }
        // 创建默认标签
        const defaultTags = [
            { name: 'JavaScript', description: 'JavaScript相关内容', slug: 'javascript' },
            { name: 'Vue.js', description: 'Vue.js框架相关', slug: 'vuejs' },
            { name: 'Node.js', description: 'Node.js后端开发', slug: 'nodejs' },
            { name: '数据库', description: '数据库相关技术', slug: 'database' }
        ];
        for (const tagData of defaultTags) {
            const exists = await Tag_1.default.findOne({ where: { slug: tagData.slug } });
            if (!exists) {
                await Tag_1.default.create(tagData);
                console.log(`创建标签: ${tagData.name}`);
            }
        }
        // 创建系统设置
        const defaultSettings = [
            { group: 'site', key: 'title', value: '内部知识库', description: '网站标题' },
            { group: 'site', key: 'description', value: '企业内部知识分享平台', description: '网站描述' },
            { group: 'system', key: 'version', value: '1.0.0', description: '系统版本' }
        ];
        for (const settingData of defaultSettings) {
            const exists = await Setting_1.default.findOne({
                where: {
                    group: settingData.group,
                    key: settingData.key
                }
            });
            if (!exists) {
                await Setting_1.default.create(settingData);
                console.log(`创建设置: ${settingData.group}.${settingData.key}`);
            }
        }
        console.log('种子数据创建完成！');
    }
    catch (error) {
        console.error('创建种子数据失败:', error.message);
    }
    finally {
        await db_1.sequelize.close();
        console.log('数据库连接已关闭');
        process.exit(0);
    }
};
// 执行初始化
seedData();
