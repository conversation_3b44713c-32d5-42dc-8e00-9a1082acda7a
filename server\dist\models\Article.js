"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Article = void 0;
const sequelize_1 = require("sequelize");
const db_1 = require("../config/db");
class Article extends sequelize_1.Model {
}
exports.Article = Article;
Article.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    },
    title: {
        type: sequelize_1.DataTypes.STRING(200),
        allowNull: false,
        validate: {
            notEmpty: { msg: '请提供文章标题' },
            len: { args: [1, 200], msg: '标题不能超过200个字符' }
        }
    },
    content: {
        type: sequelize_1.DataTypes.TEXT('long'),
        allowNull: false,
        validate: {
            notEmpty: { msg: '请提供文章内容' }
        }
    },
    summary: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        validate: {
            len: { args: [0, 500], msg: '摘要不能超过500个字符' }
        }
    },
    slug: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: false,
        unique: true
    },
    cover_image: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: true,
        defaultValue: ''
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('draft', 'published', 'archived'),
        defaultValue: 'draft'
    },
    visibility: {
        type: sequelize_1.DataTypes.ENUM('public', 'private', 'custom', 'group'),
        defaultValue: 'public'
    },
    view_count: {
        type: sequelize_1.DataTypes.INTEGER,
        defaultValue: 0
    },
    author_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'users',
            key: 'id'
        }
    },
    category_id: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: 'categories',
            key: 'id'
        }
    },
    published_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        defaultValue: sequelize_1.DataTypes.NOW
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        defaultValue: sequelize_1.DataTypes.NOW
    }
}, {
    sequelize: db_1.sequelize,
    modelName: 'Article',
    tableName: 'articles',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    hooks: {
        beforeCreate: (instance) => {
            // 生成slug
            if (!instance.slug || instance.slug === '') {
                instance.slug = instance.title
                    .toLowerCase()
                    .replace(/[^\w\s]/g, '')
                    .replace(/\s+/g, '-');
                // 如果生成的slug为空，使用时间戳和随机字符串
                if (!instance.slug || instance.slug === '') {
                    const timestamp = Date.now();
                    const randomStr = Math.random().toString(36).substring(2, 8);
                    instance.slug = `article-${timestamp}-${randomStr}`;
                }
            }
            // 设置发布日期
            if (instance.status === 'published' && !instance.published_at) {
                instance.published_at = new Date();
            }
        },
        beforeUpdate: (instance) => {
            // 生成slug
            if (instance.changed('title') && (!instance.slug || instance.slug === '')) {
                instance.slug = instance.title
                    .toLowerCase()
                    .replace(/[^\w\s]/g, '')
                    .replace(/\s+/g, '-');
                // 如果生成的slug为空，使用时间戳和随机字符串
                if (!instance.slug || instance.slug === '') {
                    const timestamp = Date.now();
                    const randomStr = Math.random().toString(36).substring(2, 8);
                    instance.slug = `article-${timestamp}-${randomStr}`;
                }
            }
            // 设置发布日期
            if (instance.changed('status') && instance.status === 'published' && !instance.published_at) {
                instance.published_at = new Date();
            }
        }
    }
});
exports.default = Article;
