2025-03-13 10:28:04:284 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:29:27:2927 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:30:19:3019 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:31:02:312 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:31:08:318 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:98:21)
2025-03-13 10:31:08:318 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:98:21)
2025-03-13 10:31:09:319 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:98:21)
2025-03-13 10:31:09:319 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:98:21)
2025-03-13 10:31:29:3129 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:32:18:3218 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "1" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:33:47:3347 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:35:18:3518 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:37:36:3736 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:38:29:3829 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:40:25:4025 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:40:43:4043 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:40:52:4052 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 10:41:23:4123 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:41:23:4123 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:41:25:4125 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:41:25:4125 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:41:30:4130 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:41:47:4147 [31merror[39m: [31m获取文章列表失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "category" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query._countDocuments (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2743:10)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticles (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:103:19)
2025-03-13 10:41:49:4149 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:41:49:4149 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:42:00:420 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:42:00:420 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:42:23:4223 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:42:23:4223 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:145:21)
2025-03-13 10:44:28:4428 [31merror[39m: [31m获取文章列表失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "category" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query._countDocuments (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2743:10)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticles (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:103:19)
2025-03-13 10:51:04:514 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 11:26:04:264 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "ces1" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 15:03:32:332 [31merror[39m: [31mtoken 验证失败: jwt malformed[39m 
JsonWebTokenError: jwt malformed
    at Object.module.exports [as verify] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\jsonwebtoken\verify.js:70:17)
    at protect (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\middleware\auth.ts:48:27)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\layer.js:95:5)
    at trim_prefix (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:328:13)
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:286:9
    at Function.process_params (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:280:10)
    at Function.handle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:175:3)
    at router (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:47:12)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\layer.js:95:5)
2025-03-13 15:52:22:5222 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:194:21)
2025-03-13 16:54:35:5435 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 17:01:02:12 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 17:01:12:112 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "2222222" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 17:06:44:644 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 17:06:55:655 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "111" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-13 17:14:27:1427 [31merror[39m: [31m文章创建失败:[39m 
ValidationError: Article validation failed: category: Cast to ObjectId failed for value "" (type string) at path "category" because of "BSONError"
    at model.Document.invalidate (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:3329:32)
    at model.$set (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:1480:12)
    at model.$set (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:1132:16)
    at model.Document (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:178:12)
    at model.Model (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:128:12)
    at new model (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:4800:15)
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:2759:20
    at Array.map (<anonymous>)
    at Function.create (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:2747:34)
    at createArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:353:35)
2025-03-13 17:25:50:2550 [31merror[39m: [31m文章创建失败:[39m 
ValidationError: Article validation failed: category: Cast to ObjectId failed for value "" (type string) at path "category" because of "BSONError"
    at model.Document.invalidate (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:3329:32)
    at model.$set (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:1480:12)
    at model.$set (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:1132:16)
    at model.Document (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:178:12)
    at model.Model (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:128:12)
    at new model (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:4800:15)
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:2759:20
    at Array.map (<anonymous>)
    at Function.create (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:2747:34)
    at createArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:353:35)
2025-03-13 17:25:54:2554 [31merror[39m: [31m文章创建失败:[39m 
ValidationError: Article validation failed: category: Cast to ObjectId failed for value "" (type string) at path "category" because of "BSONError"
    at model.Document.invalidate (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:3329:32)
    at model.$set (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:1480:12)
    at model.$set (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:1132:16)
    at model.Document (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:178:12)
    at model.Model (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:128:12)
    at new model (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:4800:15)
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:2759:20
    at Array.map (<anonymous>)
    at Function.create (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:2747:34)
    at createArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:353:35)
2025-03-13 17:26:18:2618 [31merror[39m: [31m文章创建失败:[39m 
ValidationError: Article validation failed: category: Cast to ObjectId failed for value "" (type string) at path "category" because of "BSONError"
    at model.Document.invalidate (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:3329:32)
    at model.$set (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:1480:12)
    at model.$set (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:1132:16)
    at model.Document (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:178:12)
    at model.Model (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:128:12)
    at new model (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:4800:15)
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:2759:20
    at Array.map (<anonymous>)
    at Function.create (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\model.js:2747:34)
    at createArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:353:35)
2025-03-13 17:27:56:2756 [31merror[39m: [31m文章创建失败:[39m 
ValidationError: Article validation failed: category: Path `category` is required.
    at model.Document.invalidate (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:3329:32)
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\document.js:3090:17
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1407:9
    at processTicksAndRejections (node:internal/process/task_queues:77:11)
2025-03-13 17:28:58:2858 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-14 18:43:01:431 [31merror[39m: [31m获取文章详情失败:[39m 
CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Article"
    at SchemaObjectId.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schema\objectId.js:251:11)
    at SchemaObjectId.SchemaType.applySetters (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1255:12)
    at SchemaObjectId.SchemaType.castForQuery (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\schemaType.js:1673:17)
    at cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\cast.js:390:32)
    at model.Query.Query.cast (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4907:12)
    at model.Query.Query._castConditions (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2325:10)
    at model.Query._findOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:2648:8)
    at model.Query.exec (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongoose\lib\query.js:4456:80)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:194:21)
2025-03-18 11:14:47:1447 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-18 11:15:34:1534 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-18 11:46:07:467 [31merror[39m: [31mtoken 验证失败: invalid signature[39m 
JsonWebTokenError: invalid signature
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\jsonwebtoken\verify.js:171:19
    at getSecret (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\jsonwebtoken\verify.js:97:14)
    at Object.module.exports [as verify] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\jsonwebtoken\verify.js:101:10)
    at protect (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\middleware\auth.ts:43:29)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:346:12)
2025-03-18 11:58:16:5816 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-18 11:58:19:5819 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-18 11:58:20:5820 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-20 18:14:37:1437 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "111" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-20 18:14:41:1441 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "111" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-20 18:14:45:1445 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "111" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-20 18:14:51:1451 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "111" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-20 18:15:39:1539 [31merror[39m: [31m文章创建失败:[39m 
MongoServerError: E11000 duplicate key error collection: admin-system.articles index: slug_1 dup key: { slug: "" }
    at InsertOneOperation.execute (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\insert.ts:88:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async tryOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:283:14)
    at async executeOperation (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\operations\execute_operation.ts:115:12)
    at async Collection.insertOne (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\mongodb\src\collection.ts:285:12)
2025-03-20 18:33:16:3316 [31merror[39m: [31mtoken 验证失败: invalid signature[39m 
JsonWebTokenError: invalid signature
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\jsonwebtoken\verify.js:171:19
    at getSecret (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\jsonwebtoken\verify.js:97:14)
    at Object.module.exports [as verify] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\jsonwebtoken\verify.js:101:10)
    at protect (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\middleware\auth.ts:45:29)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:346:12)
2025-03-20 18:33:18:3318 [31merror[39m: [31mtoken 验证失败: invalid signature[39m 
JsonWebTokenError: invalid signature
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\jsonwebtoken\verify.js:171:19
    at getSecret (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\jsonwebtoken\verify.js:97:14)
    at Object.module.exports [as verify] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\jsonwebtoken\verify.js:101:10)
    at protect (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\middleware\auth.ts:45:29)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\node_modules\express\lib\router\index.js:346:12)
2025-03-20 19:06:41:641 [31merror[39m: [31m获取文章详情失败:[39m 
TypeError: article.save is not a function
    at getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:339:21)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-03-20 19:07:44:744 [31merror[39m: [31m获取文章详情失败:[39m 
TypeError: article.save is not a function
    at getArticle (C:\Users\<USER>\Desktop\新建文件夹 (4)\server\src\controllers\articleController.ts:339:21)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 12:00:10:010 [31merror[39m: [31mtoken 验证失败: invalid signature[39m 
JsonWebTokenError: invalid signature
    at E:\代码管理\内部知识库\内部知识库\server\node_modules\jsonwebtoken\verify.js:171:19
    at getSecret (E:\代码管理\内部知识库\内部知识库\server\node_modules\jsonwebtoken\verify.js:97:14)
    at Object.module.exports [as verify] (E:\代码管理\内部知识库\内部知识库\server\node_modules\jsonwebtoken\verify.js:101:10)
    at protect (E:\代码管理\内部知识库\内部知识库\server\src\middleware\auth.ts:45:29)
    at Layer.handle [as handle_request] (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\layer.js:95:5)
    at next (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\layer.js:95:5)
    at E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\index.js:346:12)
2025-07-31 13:59:33:5933 [31merror[39m: [31mtoken 验证失败: jwt expired[39m 
TokenExpiredError: jwt expired
    at E:\代码管理\内部知识库\内部知识库\server\node_modules\jsonwebtoken\verify.js:190:21
    at getSecret (E:\代码管理\内部知识库\内部知识库\server\node_modules\jsonwebtoken\verify.js:97:14)
    at Object.module.exports [as verify] (E:\代码管理\内部知识库\内部知识库\server\node_modules\jsonwebtoken\verify.js:101:10)
    at protect (E:\代码管理\内部知识库\内部知识库\server\src\middleware\auth.ts:45:29)
    at Layer.handle [as handle_request] (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\layer.js:95:5)
    at next (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\layer.js:95:5)
    at E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\index.js:346:12)
2025-07-31 13:59:33:5933 [31merror[39m: [31mtoken 验证失败: jwt expired[39m 
TokenExpiredError: jwt expired
    at E:\代码管理\内部知识库\内部知识库\server\node_modules\jsonwebtoken\verify.js:190:21
    at getSecret (E:\代码管理\内部知识库\内部知识库\server\node_modules\jsonwebtoken\verify.js:97:14)
    at Object.module.exports [as verify] (E:\代码管理\内部知识库\内部知识库\server\node_modules\jsonwebtoken\verify.js:101:10)
    at protect (E:\代码管理\内部知识库\内部知识库\server\src\middleware\auth.ts:45:29)
    at Layer.handle [as handle_request] (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\layer.js:95:5)
    at next (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\layer.js:95:5)
    at E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (E:\代码管理\内部知识库\内部知识库\server\node_modules\express\lib\router\index.js:346:12)
