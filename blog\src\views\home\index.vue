<template>
  <div class="home-page">
    <!-- 头部搜索区域 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">探索知识的海洋</h1>
        <p class="hero-subtitle">发现精彩的技术文章和见解</p>
        <div class="search-box">
          <n-input-group>
            <n-input
              v-model:value="searchQuery"
              placeholder="搜索文章..."
              @keyup.enter="handleSearch"
              class="search-input"
            >
              <template #prefix>
                <n-icon size="18" class="search-icon">
                  <Search />
                </n-icon>
              </template>
            </n-input>
            <n-button 
              type="primary" 
              class="search-button"
              :disabled="!searchQuery.trim()"
              @click="handleSearch"
            >
              搜索
            </n-button>
          </n-input-group>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-section">
      <!-- 最新文章 -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">最新文章</h2>
        </div>
        
        <n-spin :show="loading">
          <div class="articles-grid">
            <n-card
              v-for="article in latestArticles"
              :key="article.id"
              class="article-card"
              :bordered="false"
              @click="handleArticleClick(article.id)"
            >
              <!-- 文章卡片内容 -->
              <div class="article-wrapper">
                <!-- 顶部：分类和时间 -->
                <div class="article-meta-top">
                  <n-tag 
                    v-if="article.category" 
                    size="small" 
                    type="primary" 
                    class="category-tag"
                    :bordered="false"
                    @click.stop="handleCategoryClick(article.category?.id)"
                  >
                    {{ article.category.name }}
                  </n-tag>
                  <span class="article-date">{{ formatDate(article.created_at) }}</span>
                </div>

                <!-- 文章主体内容 -->
                <div class="article-main-content">
                  <div class="article-cover" v-if="article.cover_image">
                    <img :src="article.cover_image" :alt="article.title">
                  </div>
                  <div class="article-content">
                    <h3 class="article-title">{{ article.title }}</h3>
                    <p class="article-summary">{{ article.summary || article.content.substring(0, 100) }}...</p>
                  </div>
                </div>

                <!-- 底部：作者信息、标签和浏览量 -->
                <div class="article-footer">
                  <div class="article-meta-bottom">
                    <!-- 左侧：作者头像和名字 -->
                    <div class="meta-item">
                      <n-avatar
                        round
                        size="small"
                        :src="article.author?.avatar"
                      >
                        {{ article.author?.username?.charAt(0)?.toUpperCase() }}
                      </n-avatar>
                      <span class="author-name">{{ article.author?.username }}</span>
                    </div>

                    <!-- 中间：标签 -->
                    <div class="meta-item tags-wrapper" v-if="article.tags && article.tags.length > 0">
                      <n-tag
                        v-for="tag in article.tags"
                        :key="tag.id"
                        size="small"
                        type="info"
                        class="article-tag"
                        :bordered="false"
                        @click.stop="handleTagClick(tag.id)"
                      >
                        {{ tag.name }}
                      </n-tag>
                    </div>

                    <!-- 右侧：浏览量 -->
                    <div class="meta-item views">
                      <n-space align="center" :size="4">
                        <n-icon><Eye /></n-icon>
                        <span>{{ formatNumber(article.view_count) }}</span>
                      </n-space>
                    </div>
                  </div>
                </div>
              </div>
            </n-card>
          </div>
          
          <n-empty v-if="!loading && latestArticles.length === 0" description="暂无文章" />
        </n-spin>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search, ChevronForward, Eye } from '@vicons/ionicons5'
import { articleApi } from '@/api'
import { formatDate, formatNumber } from '@/utils/format'
import {
  NCard,
  NSpin,
  NSpace,
  NInput,
  NInputGroup,
  NButton,
  NIcon,
  NEmpty,
  NAvatar,
  NTag,
  useMessage
} from 'naive-ui'

const router = useRouter()
const message = useMessage()

// 状态
const loading = ref(false)
const searchQuery = ref('')
const latestArticles = ref<any[]>([])

// 获取最新文章
const fetchLatestArticles = async () => {
  try {
    loading.value = true
    const response = await articleApi.getArticles({
      page: 1,
      limit: 6,
      order: '-created_at'
    })

    console.log('获取最新文章响应:', response)

    // 兼容不同的响应格式
    if (response.data?.code === 0 || response.data?.code === 200) {
      // 处理分页数据格式
      if (response.data.data && response.data.data.items) {
        latestArticles.value = response.data.data.items
      } else if (Array.isArray(response.data.data)) {
        latestArticles.value = response.data.data
      } else {
        latestArticles.value = []
      }
      console.log('处理后的最新文章:', latestArticles.value)
    } else {
      console.warn('获取最新文章失败，响应格式不正确:', response)
      latestArticles.value = []
    }
  } catch (error) {
    console.error('获取最新文章失败:', error)
    message.error('获取最新文章失败')
    latestArticles.value = []
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  if (!searchQuery.value.trim()) {
    message.warning('请输入搜索关键词')
    return
  }
  
  router.push({
    path: '/articles',
    query: { search: searchQuery.value.trim() }
  })
}

// 处理分类点击
const handleCategoryClick = (categoryId: number) => {
  console.log('点击分类:', categoryId)
  if (!categoryId) return
  router.push({
    path: '/articles',
    query: { category: categoryId.toString() }
  })
}

// 处理标签点击
const handleTagClick = (tagId: number) => {
  console.log('点击标签:', tagId)
  if (!tagId) return
  router.push({
    path: '/articles',
    query: { tag: tagId.toString() }
  })
}

// 处理文章点击
const handleArticleClick = (articleId: number) => {
  router.push({
    path: '/articles',
    query: { id: articleId.toString() }
  })
}

onMounted(() => {
  fetchLatestArticles()
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.hero-section {
  background: linear-gradient(135deg, #00B4DB, #0083B0);
  padding: 6rem 2rem;
  margin-bottom: 4rem;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/path/to/pattern.svg') center/cover;
    opacity: 0.1;
  }
  
  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    color: white;
    position: relative;
    z-index: 1;
  }

  .hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin: 0 0 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
  }

  .search-box {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    
    :deep(.n-input-group) {
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border-radius: 50px;
      padding: 6px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
      }
      
      .n-input {
        background: transparent;
        
        .n-input__input-el {
          color: white;
          font-size: 1.1rem;
          padding-left: 1rem;
          
          &::placeholder {
            color: rgba(255, 255, 255, 0.7);
          }
        }
        
        .search-icon {
          color: rgba(255, 255, 255, 0.8);
          margin-left: 0.5rem;
        }
      }
      
      .search-button {
        min-width: 100px;
        height: 40px;
        border-radius: 20px;
        font-size: 1rem;
        font-weight: 500;
        background: white;
        color: #0083B0;
        border: none;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          background: #f8f9fa;
        }
        
        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        
        &:disabled {
          background: rgba(255, 255, 255, 0.6);
          color: rgba(0, 131, 176, 0.6);
          transform: none;
          box-shadow: none;
        }
      }
    }
  }
}

.content-section {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section {
  margin-bottom: 4rem;
  width: 100%;
  max-width: 1200px;

  .section-header {
    text-align: center;
    margin-bottom: 3rem;
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    color: #2c3e50;
    position: relative;
    display: inline-block;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -12px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: #00B4DB;
      border-radius: 2px;
    }
  }
}

.articles-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin: 0 auto;
  width: 100%;
}

.article-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  transition: all 0.3s ease;
  height: 100%;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  cursor: pointer;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .article-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .article-main-content {
    flex: 1;
    text-decoration: none;
    color: inherit;
    
    &:hover {
      .article-title {
        color: #00B4DB;
      }
      
      .article-cover img {
        transform: scale(1.1);
      }
    }
  }
}

.article-meta-top {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 1rem;

  :deep(.category-tag) {
    background: linear-gradient(120deg, #00B4DB, #0083B0);
    color: white;
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .article-date {
    color: #666;
    font-size: 0.9rem;
  }
}

.article-cover {
  height: 200px;
  overflow: hidden;
  margin-bottom: 1rem;
  border-radius: 8px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
}

.article-content {
  padding: 0 0.5rem;
}

.article-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.75rem;
  color: #2c3e50;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.article-summary {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 3.2em;
}

.article-footer {
  margin-top: auto;
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.article-meta-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;

  .meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    
    &.tags-wrapper {
      flex: 1;
      overflow: hidden;
      justify-content: center;
      
      :deep(.article-tag) {
        background-color: #f0f7ff;
        color: #0083B0;
        border: 1px solid #e1f0ff;
        padding: 2px 10px;
        border-radius: 4px;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background-color: #e1f0ff;
          border-color: #0083B0;
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
    
    &.views {
      color: #666;
      font-size: 0.9rem;
      min-width: 60px;
      justify-content: flex-end;
    }
  }

  .author-name {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .articles-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 4rem 1rem;

    .hero-title {
      font-size: 2.5rem;
    }

    .hero-subtitle {
      font-size: 1.2rem;
    }

    .search-box {
      :deep(.n-input-group) {
        .search-button {
          min-width: 80px;
          height: 36px;
          font-size: 0.9rem;
        }
      }
    }
  }

  .content-section {
    padding: 0 1rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .articles-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .article-card {
    .article-cover {
      height: 180px;
    }

    .article-title {
      font-size: 1.2rem;
    }

    .article-meta-bottom {
      flex-wrap: wrap;
      
      .meta-item.tags-wrapper {
        order: 3;
        width: 100%;
        justify-content: flex-start;
        margin-top: 0.5rem;
      }
    }
  }
}
</style> 