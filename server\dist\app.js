"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const morgan_1 = __importDefault(require("morgan"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const appError_1 = __importDefault(require("./utils/appError"));
const error_1 = require("./middleware/error");
const settingRoutes_1 = __importDefault(require("./routes/settingRoutes"));
const announcementRoutes_1 = __importDefault(require("./routes/announcementRoutes"));
const authRoutes_1 = __importDefault(require("./routes/authRoutes"));
const userRoutes_1 = __importDefault(require("./routes/userRoutes"));
const userGroupRoutes_1 = __importDefault(require("./routes/userGroupRoutes"));
const dashboardRoutes_1 = __importDefault(require("./routes/dashboardRoutes"));
const articleRoutes_1 = __importDefault(require("./routes/articleRoutes"));
const categoryRoutes_1 = __importDefault(require("./routes/categoryRoutes"));
const tagRoutes_1 = __importDefault(require("./routes/tagRoutes"));
const uploadRoutes_1 = __importDefault(require("./routes/uploadRoutes"));
const path_1 = __importDefault(require("path"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const app = (0, express_1.default)();
// 安全相关中间件
app.use((0, helmet_1.default)({
    contentSecurityPolicy: false // 禁用 CSP 以允许前端资源加载
}));
// CORS 配置
app.use((0, cors_1.default)({
    origin: true, // 允许所有来源
    credentials: true, // 允许跨域请求携带凭据
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
// MongoDB sanitize 已移除，使用MySQL
// 解析请求
app.use(express_1.default.json({ limit: '500mb' }));
app.use(express_1.default.urlencoded({ extended: false }));
app.use((0, cookie_parser_1.default)()); // 解析 Cookie
// 日志
if (process.env.NODE_ENV === 'development') {
    app.use((0, morgan_1.default)('dev'));
    console.log('Running in development mode');
}
else {
    console.log('Running in production mode');
}
// 压缩响应
app.use((0, compression_1.default)());
// 创建 API 路由器
const apiRouter = express_1.default.Router();
// API 路由注册
apiRouter.use('/auth', authRoutes_1.default);
apiRouter.use('/users', userRoutes_1.default);
apiRouter.use('/user-groups', userGroupRoutes_1.default);
apiRouter.use('/dashboard', dashboardRoutes_1.default);
apiRouter.use('/articles', articleRoutes_1.default);
apiRouter.use('/categories', categoryRoutes_1.default);
apiRouter.use('/tags', tagRoutes_1.default);
apiRouter.use('/announcements', announcementRoutes_1.default);
apiRouter.use('/settings', settingRoutes_1.default);
apiRouter.use('/upload', uploadRoutes_1.default);
// 健康检查端点
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV
    });
});
// 将 API 路由器挂载到主应用
app.use('/api', apiRouter);
// 静态文件服务
app.use('/uploads', express_1.default.static(path_1.default.join(__dirname, '../uploads')));
// 前端静态文件服务 - 博客前台
app.use('/', express_1.default.static(path_1.default.join(__dirname, '../public/blog')));
// 前端静态文件服务 - 管理后台
app.use('/admin', express_1.default.static(path_1.default.join(__dirname, '../public/admin')));
// 设置信任代理，解决 express-rate-limit 的警告
app.set('trust proxy', 1);
// 请求限制
const limiter = (0, express_rate_limit_1.default)({
    max: 1000,
    windowMs: 5 * 60 * 1000,
    message: '请求过于频繁，请稍后再试',
    // 添加以下配置来解决X-Forwarded-For警告
    standardHeaders: true,
    legacyHeaders: false
});
app.use(limiter);
// 前端路由处理 - 博客前台
app.get('*', (req, res, next) => {
    // 如果是API请求或管理后台请求，跳过该中间件
    if (req.path.startsWith('/api/') || req.path.startsWith('/admin/')) {
        return next();
    }
    // 服务博客前台的HTML文件
    res.sendFile(path_1.default.join(__dirname, '../public/blog/index.html'));
});
// 前端路由处理 - 管理后台
app.get('/admin/*', (req, res) => {
    res.sendFile(path_1.default.join(__dirname, '../public/admin/index.html'));
});
// 处理未匹配的路由
app.all('*', (req, res, next) => {
    console.log(`404 - Not Found: ${req.method} ${req.originalUrl}`);
    next(new appError_1.default(`找不到路径: ${req.originalUrl}`, 404));
});
// 错误处理
app.use(error_1.errorHandler);
exports.default = app;
