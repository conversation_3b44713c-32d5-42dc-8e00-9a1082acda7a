<template>
  <div class="categories-page">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <h1>文章分类</h1>
        <p class="subtitle">探索不同领域的精彩内容</p>
      </div>
    </div>

    <div class="categories-container">
      <!-- 分类列表 -->
      <div class="categories-grid">
        <n-card 
          v-for="category in categories" 
          :key="category._id" 
          :bordered="false" 
          class="category-card"
          @click="handleCategoryClick(category)"
        >
          <div class="category-content">
            <!-- 图标区域 -->
            <div class="category-icon">
              <n-icon :size="32">
                <component :is="getCategoryIcon(category.name)" />
              </n-icon>
            </div>

            <!-- 文本信息 -->
            <div class="category-info">
              <h3 class="category-name">{{ category.name }}</h3>
              <p class="category-desc">{{ category.description || '暂无描述' }}</p>
            </div>

            <!-- 统计信息 -->
            <div class="category-stats">
              <div class="stat-item">
                <n-icon><DocumentTextOutline /></n-icon>
                <span>{{ category.article_count || 0 }} 篇文章</span>
              </div>
            </div>

            <!-- 查看按钮 -->
            <div class="category-action">
              <n-button text class="view-btn">
                浏览文章
                <template #icon>
                  <n-icon><ChevronForward /></n-icon>
                </template>
              </n-button>
            </div>
          </div>
        </n-card>
      </div>

      <!-- 空状态 -->
      <n-empty 
        v-if="categories.length === 0" 
        description="暂无分类"
        size="large"
      >
        <template #icon>
          <n-icon size="48" depth="3">
            <FileTrayStackedOutline />
          </n-icon>
        </template>
      </n-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { categoryApi } from '@/api'
import {
  BookOutline,
  CodeSlashOutline,
  ServerOutline,
  LayersOutline,
  ColorPaletteOutline,
  BuildOutline,
  DocumentTextOutline,
  ChevronForward,
  FileTrayStackedOutline
} from '@vicons/ionicons5'
import {
  NCard,
  NIcon,
  NEmpty,
  NButton,
  useMessage
} from 'naive-ui'

const router = useRouter()
const message = useMessage()
const categories = ref<any[]>([])

// 根据分类名称获取对应图标
const getCategoryIcon = (name: string) => {
  const iconMap: Record<string, any> = {
    '前端开发': CodeSlashOutline,
    '后端开发': ServerOutline,
    '设计': ColorPaletteOutline,
    '架构': LayersOutline,
    '工具': BuildOutline
  }
  return iconMap[name] || BookOutline
}

// 加载分类列表
const fetchCategories = async () => {
  try {
    const res = await categoryApi.getCategories()
    console.log('获取到的分类响应:', res)
    if (res.data.code === 0) {
      categories.value = res.data.data.map(category => ({
        ...category,
        id: category.id,
        article_count: category.article_count || 0
      }))
      console.log('处理后的分类数据:', categories.value)
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    message.error('获取分类列表失败')
  }
}

// 处理分类点击
const handleCategoryClick = (category: any) => {
  router.push({
    path: '/articles',
    query: { category: category.id }
  })
}

onMounted(() => {
  fetchCategories()
})
</script>

<style lang="scss" scoped>
.categories-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem 0;
}

.page-header {
  background: linear-gradient(135deg, #00B4DB, #0083B0);
  padding: 4rem 2rem;
  margin-bottom: 3rem;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
    opacity: 0.6;
  }

  .header-content {
    position: relative;
    z-index: 1;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 1rem;
      background: linear-gradient(120deg, #fff, #e0f7fa);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .subtitle {
      font-size: 1.2rem;
      opacity: 0.9;
      margin: 0;
    }
  }
}

.categories-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.category-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

    .category-icon {
      transform: scale(1.1);
      color: var(--n-primary-color);
    }

    .view-btn {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .category-content {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .category-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, rgba(0,180,219,0.1), rgba(0,131,176,0.1));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2c3e50;
    transition: all 0.3s ease;
  }

  .category-info {
    flex: 1;

    .category-name {
      font-size: 1.25rem;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 0.5rem;
    }

    .category-desc {
      color: #666;
      font-size: 0.95rem;
      line-height: 1.5;
      margin: 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .category-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.06);

    .stat-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #666;
      font-size: 0.95rem;

      .n-icon {
        color: var(--n-primary-color);
      }
    }
  }

  .category-action {
    display: flex;
    justify-content: flex-end;

    .view-btn {
      opacity: 0;
      transform: translateX(-10px);
      transition: all 0.3s ease;
      color: var(--n-primary-color);
      
      &:hover {
        color: var(--n-primary-color-hover);
      }

      .n-icon {
        transition: transform 0.3s ease;
      }

      &:hover .n-icon {
        transform: translateX(4px);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    padding: 3rem 1rem;

    h1 {
      font-size: 2rem;
    }

    .subtitle {
      font-size: 1rem;
    }
  }

  .categories-container {
    padding: 0 1rem;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .category-card {
    .category-content {
      padding: 1.5rem;
    }

    .view-btn {
      opacity: 1;
      transform: none;
    }
  }
}

// 修改暗色主题样式部分
:deep(.dark) {
  .page-header {
    background: linear-gradient(135deg, #1a1a1a, #2c3e50);

    &::before {
      background: radial-gradient(circle at center, rgba(255,255,255,0.05) 0%, transparent 70%);
    }
  }

  .category-card {
    background: rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    }

    .category-icon {
      background: linear-gradient(135deg, rgba(255,255,255,0.05), rgba(255,255,255,0.02));
      color: #e5eaf3;
    }

    .category-info {
      .category-name {
        color: #e5eaf3;
      }

      .category-desc {
        color: rgba(229, 234, 243, 0.6);
      }
    }

    .category-stats {
      border-top-color: rgba(255, 255, 255, 0.1);

      .stat-item {
        color: rgba(229, 234, 243, 0.6);

        .n-icon {
          color: var(--n-primary-color);
        }
      }
    }

    .category-action {
      .view-btn {
        color: var(--n-primary-color);

        &:hover {
          color: var(--n-primary-color-hover);
        }
      }
    }
  }
}
</style> 