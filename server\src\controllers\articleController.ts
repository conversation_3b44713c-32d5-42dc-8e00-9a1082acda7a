import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { Op, Order } from 'sequelize';
import Article from '../models/Article';
import User from '../models/User';
import Category from '../models/Category';
import Tag from '../models/Tag';
import { ErrorResponse } from '../middleware/error';
import logger from '../utils/logger';

// 获取文章列表
export const getArticles = async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 10,
      order = '-created_at',
      search,
      category,
      tag,
      status,
      isUser = false,
      isAdmin = false
    } = req.query;

    const pageNum = parseInt(page as string);
    const pageSize = parseInt(limit as string);
    const offset = (pageNum - 1) * pageSize;

    // 构建查询条件
    const whereClause: any = {};

    // 如果是前台用户请求，只显示已发布的公开文章
    if (isUser === 'true') {
      whereClause.status = 'published';
      whereClause.visibility = 'public';
    }
    // 如果是管理员请求，可以看到所有文章（不添加额外限制）
    // isAdmin === 'true' 时不添加任何状态或可见性限制

    // 搜索条件
    if (search) {
      whereClause[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { content: { [Op.like]: `%${search}%` } },
        { summary: { [Op.like]: `%${search}%` } }
      ];
    }

    // 分类筛选
    if (category) {
      whereClause.category_id = category;
    }

    // 状态筛选
    if (status) {
      whereClause.status = status;
    }

    // 构建include条件
    const includeConditions: any[] = [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'username', 'nickname', 'avatar']
      },
      {
        model: Category,
        as: 'category',
        attributes: ['id', 'name', 'slug']
      },
      {
        model: Tag,
        as: 'tags',
        attributes: ['id', 'name', 'slug'],
        through: { attributes: [] }
      }
    ];

    // 标签筛选 - 需要特殊处理
    if (tag) {
      includeConditions[2] = {
        model: Tag,
        as: 'tags',
        attributes: ['id', 'name', 'slug'],
        through: { attributes: [] },
        where: { id: tag },
        required: true // 内连接，确保只返回包含指定标签的文章
      };
    }

    // 排序处理
    let orderClause: Order = [['created_at', 'DESC']];
    if (order) {
      const orderStr = order as string;
      if (orderStr.startsWith('-')) {
        orderClause = [[orderStr.substring(1), 'DESC']];
      } else {
        orderClause = [[orderStr, 'ASC']];
      }
    }

    // 查询文章
    const { count, rows: articles } = await Article.findAndCountAll({
      where: whereClause,
      include: includeConditions,
      order: orderClause,
      offset,
      limit: pageSize,
      distinct: true // 当使用标签筛选时，避免重复计数
    });

    res.status(200).json({
      code: 200,
      message: '获取文章列表成功',
      data: {
        total: count,
        items: articles,
        page: pageNum,
        pageSize,
        totalPages: Math.ceil(count / pageSize)
      }
    });
  } catch (error: any) {
    logger.error('获取文章列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取文章列表失败',
      error: error.message
    });
  }
};

// 获取单个文章
export const getArticle = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const article = await Article.findByPk(id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username', 'nickname', 'avatar']
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'slug'],
          through: { attributes: [] }
        }
      ]
    });

    if (!article) {
      return res.status(404).json({
        code: 404,
        message: '文章不存在'
      });
    }

    // 增加浏览量
    await article.increment('view_count');

    res.status(200).json({
      code: 200,
      message: '获取文章成功',
      data: article
    });
  } catch (error: any) {
    logger.error('获取文章失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取文章失败',
      error: error.message
    });
  }
};

// 创建文章
export const createArticle = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '输入数据验证失败',
        errors: errors.array()
      });
    }

    const {
      title,
      content,
      summary,
      slug,
      cover_image,
      status = 'draft',
      visibility = 'public',
      category_id,
      tags = []
    } = req.body;

    // 创建文章
    const article = await Article.create({
      title,
      content,
      summary,
      slug: slug || title.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, '-'),
      cover_image,
      status,
      visibility,
      category_id,
      author_id: req.user?.id,
      view_count: 0
    });

    // 关联标签
    if (tags && tags.length > 0) {
      const tagInstances = await Tag.findAll({
        where: { id: { [Op.in]: tags } }
      });
      await (article as any).setTags(tagInstances);
    }

    // 获取完整的文章信息
    const fullArticle = await Article.findByPk(article.id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username', 'nickname', 'avatar']
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'slug'],
          through: { attributes: [] }
        }
      ]
    });

    res.status(201).json({
      code: 201,
      message: '创建文章成功',
      data: fullArticle
    });
  } catch (error: any) {
    logger.error('创建文章失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建文章失败',
      error: error.message
    });
  }
};

// 更新文章
export const updateArticle = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const article = await Article.findByPk(id);
    if (!article) {
      return res.status(404).json({
        code: 404,
        message: '文章不存在'
      });
    }

    // 更新文章
    await article.update(updates);

    // 更新标签关联
    if (updates.tags) {
      const tagInstances = await Tag.findAll({
        where: { id: { [Op.in]: updates.tags } }
      });
      await (article as any).setTags(tagInstances);
    }

    // 获取更新后的完整文章信息
    const updatedArticle = await Article.findByPk(id, {
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username', 'nickname', 'avatar']
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'slug'],
          through: { attributes: [] }
        }
      ]
    });

    res.status(200).json({
      code: 200,
      message: '更新文章成功',
      data: updatedArticle
    });
  } catch (error: any) {
    logger.error('更新文章失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新文章失败',
      error: error.message
    });
  }
};

// 删除文章
export const deleteArticle = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const article = await Article.findByPk(id);
    if (!article) {
      return res.status(404).json({
        code: 404,
        message: '文章不存在'
      });
    }

    await article.destroy();

    res.status(200).json({
      code: 200,
      message: '删除文章成功'
    });
  } catch (error: any) {
    logger.error('删除文章失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除文章失败',
      error: error.message
    });
  }
};

// 获取热门文章
export const getHotArticles = async (req: Request, res: Response) => {
  try {
    const { limit = 10 } = req.query;
    const pageSize = parseInt(limit as string);

    const articles = await Article.findAll({
      where: {
        status: 'published',
        visibility: 'public'
      },
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username', 'nickname', 'avatar']
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        }
      ],
      order: [['view_count', 'DESC']],
      limit: pageSize
    });

    res.status(200).json({
      code: 200,
      message: '获取热门文章成功',
      data: articles
    });
  } catch (error: any) {
    logger.error('获取热门文章失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取热门文章失败',
      error: error.message
    });
  }
};
