"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const dotenv_1 = __importDefault(require("dotenv"));
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = require("../config/db");
const models_1 = require("../models");
dotenv_1.default.config();
async function migrateData() {
    try {
        console.log('开始数据迁移...');
        // 连接MongoDB
        if (process.env.MONGODB_URI) {
            await mongoose_1.default.connect(process.env.MONGODB_URI);
            console.log('MongoDB连接成功');
        }
        else {
            console.log('未配置MONGODB_URI，跳过数据迁移');
            return;
        }
        // 连接MySQL
        await db_1.sequelize.authenticate();
        (0, models_1.setupAssociations)();
        await db_1.sequelize.sync({ force: true }); // 清空并重建表
        console.log('MySQL连接成功，表结构已重建');
        // 创建ID映射
        const userIdMap = new Map();
        const categoryIdMap = new Map();
        const tagIdMap = new Map();
        const userGroupIdMap = new Map();
        // 迁移用户数据
        console.log('迁移用户数据...');
        const mongoUsers = await mongoose_1.default.connection.db.collection('users').find({}).toArray();
        for (const mongoUser of mongoUsers) {
            const user = await models_1.User.create({
                username: mongoUser.username,
                email: mongoUser.email,
                password: mongoUser.password, // 密码已经加密，直接使用
                role: mongoUser.role,
                nickname: mongoUser.nickname,
                bio: mongoUser.bio,
                avatar: mongoUser.avatar || '/uploads/touxiang.png',
                is_active: mongoUser.is_active,
                last_login: mongoUser.last_login,
                created_at: mongoUser.created_at,
                updated_at: mongoUser.updated_at,
            });
            userIdMap.set(mongoUser._id, user.id);
        }
        console.log(`用户数据迁移完成: ${mongoUsers.length} 条记录`);
        // 迁移分类数据
        console.log('迁移分类数据...');
        const mongoCategories = await mongoose_1.default.connection.db.collection('categories').find({}).toArray();
        for (const mongoCategory of mongoCategories) {
            const category = await models_1.Category.create({
                name: mongoCategory.name,
                description: mongoCategory.description,
                slug: mongoCategory.slug,
                created_at: mongoCategory.created_at,
                updated_at: mongoCategory.updated_at,
            });
            categoryIdMap.set(mongoCategory._id, category.id);
        }
        console.log(`分类数据迁移完成: ${mongoCategories.length} 条记录`);
        // 迁移标签数据
        console.log('迁移标签数据...');
        const mongoTags = await mongoose_1.default.connection.db.collection('tags').find({}).toArray();
        for (const mongoTag of mongoTags) {
            const tag = await models_1.Tag.create({
                name: mongoTag.name,
                description: mongoTag.description,
                slug: mongoTag.slug,
                created_at: mongoTag.created_at,
                updated_at: mongoTag.updated_at,
            });
            tagIdMap.set(mongoTag._id, tag.id);
        }
        console.log(`标签数据迁移完成: ${mongoTags.length} 条记录`);
        console.log('数据迁移完成！');
    }
    catch (error) {
        console.error('数据迁移失败:', error);
    }
    finally {
        await mongoose_1.default.disconnect();
        await db_1.sequelize.close();
        process.exit(0);
    }
}
migrateData();
