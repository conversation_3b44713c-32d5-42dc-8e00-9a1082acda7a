import { DataTypes, Model, Optional } from 'sequelize';
import bcrypt from 'bcryptjs';
import { sequelize } from '../config/db';

export interface IUser {
  id: number;
  username: string;
  email: string;
  password: string;
  role: 'user' | 'admin';
  nickname?: string;
  bio?: string;
  avatar: string;
  is_active: boolean;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

interface UserCreationAttributes extends Optional<IUser, 'id' | 'avatar' | 'created_at' | 'updated_at'> {}

export class User extends Model<IUser, UserCreationAttributes> implements IUser {
  public id!: number;
  public username!: string;
  public email!: string;
  public password!: string;
  public role!: 'user' | 'admin';
  public nickname?: string;
  public bio?: string;
  public avatar!: string;
  public is_active!: boolean;
  public last_login?: Date;
  public created_at!: Date;
  public updated_at!: Date;



  // 比较密码方法
  async comparePassword(candidatePassword: string): Promise<boolean> {
    try {
      return await bcrypt.compare(candidatePassword, this.password);
    } catch (error) {
      return false;
    }
  }
}

User.init({
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: { msg: '用户名是必需的' },
      len: { args: [1, 50], msg: '用户名长度必须在1-50个字符之间' }
    }
  },
  email: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: { msg: '请输入有效的邮箱地址' },
      notEmpty: { msg: '邮箱是必需的' }
    }
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: { msg: '密码是必需的' },
      len: { args: [6, 255], msg: '密码长度至少6个字符' }
    }
  },
  role: {
    type: DataTypes.ENUM('user', 'admin'),
    defaultValue: 'user'
  },
  nickname: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      len: { args: [0, 50], msg: '昵称不能超过50个字符' }
    }
  },
  bio: {
    type: DataTypes.TEXT,
    allowNull: true,
    validate: {
      len: { args: [0, 500], msg: '个人简介不能超过500个字符' }
    }
  },
  avatar: {
    type: DataTypes.STRING(255),
    defaultValue: '/uploads/touxiang.png'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  last_login: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'User',
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  hooks: {
    beforeCreate: async (instance: User) => {
      if (instance.password) {
        const salt = await bcrypt.genSalt(12);
        instance.password = await bcrypt.hash(instance.password, salt);
      }
    },
    beforeUpdate: async (instance: User) => {
      if (instance.changed('password')) {
        const salt = await bcrypt.genSalt(12);
        instance.password = await bcrypt.hash(instance.password, salt);
      }
    }
  }
});

export default User;