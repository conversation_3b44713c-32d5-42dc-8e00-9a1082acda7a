<template>
  <div class="profile-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1>个人中心</h1>
        <p class="subtitle">管理您的个人信息和文章</p>
      </div>
    </div>

    <div class="content-wrapper">
      <div class="profile-layout">
        <!-- 左侧用户信息卡片 -->
        <div class="profile-sidebar">
          <div class="user-card">
            <div class="avatar-wrapper">
              <n-avatar
                :key="formData.avatar"
                :size="120"
                round
                :src="formData.avatar"
                @error="handleAvatarError"
                class="user-avatar"
              >
                {{ formData.nickname?.charAt(0)?.toUpperCase() || formData.username?.charAt(0)?.toUpperCase() || 'U' }}
              </n-avatar>
              <div class="avatar-edit-badge">
                <n-upload
                  :custom-request="handleAvatarUpload"
                  accept="image/*"
                  :max="1"
                  :show-file-list="false"
                >
                  <n-button circle size="small">
                    <template #icon>
                      <n-icon><CreateOutline /></n-icon>
                    </template>
                  </n-button>
                </n-upload>
              </div>
            </div>
            <h2 class="user-name">{{ formData.nickname || formData.username }}</h2>
            <p class="user-bio">{{ formData.bio || '这个人很懒，还没有填写个人简介' }}</p>
            <div class="user-stats">
              <div class="stat-item">
                <span class="stat-value">{{ articles.filter(a => a.status === 'published').length }}</span>
                <span class="stat-label">已发布</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ articles.filter(a => a.status === 'draft').length }}</span>
                <span class="stat-label">草稿</span>
              </div>
            </div>
            <div class="avatar-debug" v-if="showDebugInfo">
              <p>头像URL: {{ formData.avatar }}</p>
              <n-button size="small" @click="fixAvatar">修复头像</n-button>
            </div>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="profile-content">
          <n-tabs type="segment" animated class="profile-tabs" v-model:value="activeProfileTab">
            <n-tab-pane name="info" tab="个人信息">
              <!-- 用户信息表单 -->
              <n-card class="section-card" :bordered="false">
                <n-form
                  ref="formRef"
                  :model="formData"
                  :rules="rules"
                  label-placement="left"
                  label-width="100"
                  require-mark-placement="right-hanging"
                >
                  <n-grid :cols="2" :x-gap="24" responsive="screen">
                    <n-grid-item>
                      <n-form-item label="用户名" path="username">
                        <n-input
                          v-model:value="formData.username"
                          placeholder="请输入用户名"
                          disabled
                        />
                      </n-form-item>
                    </n-grid-item>

                    <n-grid-item>
                      <n-form-item label="昵称" path="nickname">
                        <n-input
                          v-model:value="formData.nickname"
                          placeholder="请输入昵称"
                        />
                      </n-form-item>
                    </n-grid-item>

                    <n-grid-item span="2">
                      <n-form-item label="邮箱" path="email">
                        <n-input
                          v-model:value="formData.email"
                          placeholder="请输入邮箱"
                        />
                      </n-form-item>
                    </n-grid-item>

                    <n-grid-item span="2">
                      <n-form-item label="个人简介" path="bio">
                        <n-input
                          v-model:value="formData.bio"
                          type="textarea"
                          placeholder="请输入个人简介"
                          :autosize="{ minRows: 3, maxRows: 5 }"
                        />
                      </n-form-item>
                    </n-grid-item>
                  </n-grid>

                  <div class="form-actions">
                    <n-button
                      type="primary"
                      @click="handleSave"
                      :loading="saving"
                      size="large"
                      round
                    >
                      保存修改
                    </n-button>
                  </div>
                </n-form>
              </n-card>
            </n-tab-pane>

            <n-tab-pane name="password" tab="修改密码">
              <!-- 修改密码卡片 -->
              <n-card class="section-card" :bordered="false">
                <n-form
                  ref="passwordFormRef"
                  :model="passwordForm"
                  :rules="passwordRules"
                  label-placement="left"
                  label-width="100"
                >
                  <n-form-item label="当前密码" path="currentPassword">
                    <n-input
                      v-model:value="passwordForm.currentPassword"
                      type="password"
                      show-password-on="click"
                      placeholder="请输入当前密码"
                    />
                  </n-form-item>

                  <n-form-item label="新密码" path="newPassword">
                    <n-input
                      v-model:value="passwordForm.newPassword"
                      type="password"
                      show-password-on="click"
                      placeholder="请输入新密码"
                    />
                  </n-form-item>

                  <n-form-item label="确认新密码" path="confirmPassword">
                    <n-input
                      v-model:value="passwordForm.confirmPassword"
                      type="password"
                      show-password-on="click"
                      placeholder="请再次输入新密码"
                    />
                  </n-form-item>

                  <div class="form-actions">
                    <n-button
                      type="primary"
                      @click="handleChangePassword"
                      :loading="changingPassword"
                      size="large"
                      round
                    >
                      修改密码
                    </n-button>
                  </div>
                </n-form>
              </n-card>
            </n-tab-pane>

            <n-tab-pane name="articles" tab="我的文章">
              <!-- 我的文章卡片 -->
              <n-card class="section-card" :bordered="false">
                <template #header>
                  <div class="section-header">
                    <h2>我的文章</h2>
                    <div class="header-extra">
                      <n-button type="primary" @click="router.push('/articles/edit')" round>
                        <template #icon>
                          <n-icon><CreateOutline /></n-icon>
                        </template>
                        写文章
                      </n-button>
                    </div>
                  </div>
                </template>

                <n-tabs type="line" animated v-model:value="activeTab" @update:value="handleTabChange" class="article-tabs">
                  <n-tab-pane name="published" tab="已发布">
                    <n-data-table
                      :columns="articleColumns"
                      :data="articles"
                      :loading="loadingArticles"
                      :pagination="pagination"
                      class="article-table"
                      :row-class-name="() => 'article-row'"
                    />
                  </n-tab-pane>
                  <n-tab-pane name="draft" tab="草稿箱">
                    <n-data-table
                      :columns="articleColumns"
                      :data="articles"
                      :loading="loadingArticles"
                      :pagination="pagination"
                      class="article-table"
                      :row-class-name="() => 'article-row'"
                    />
                  </n-tab-pane>
                </n-tabs>
              </n-card>
            </n-tab-pane>
          </n-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, h, watch } from 'vue'
import { useRouter, useRoute, RouterLink } from 'vue-router'
import { useMessage, useDialog } from 'naive-ui'
import {
  NCard,
  NForm,
  NFormItem,
  NInput,
  NButton,
  NIcon,
  NGrid,
  NGridItem,
  NAvatar,
  NUpload,
  NSpace,
  NTabs,
  NTabPane,
  NDataTable,
  type DataTableColumns,
  type FormInst as FormInstance,
  type PaginationProps,
  NTag,
  NPopconfirm
} from 'naive-ui'
import {
  PersonOutline,
  LockClosedOutline,
  DocumentTextOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'
import { formatDate } from '@/utils/format'
import { userApi, articleApi, uploadApi } from '@/api'
import type { FormRules } from '@/types/form'
import type { Article } from '@/types/api'
import { useUserStore } from '@/stores/user'
import { UPLOADS_BASE_URL } from '@/utils/request'

const router = useRouter()
const message = useMessage()
const dialog = useDialog()

interface FormState {
  username: string
  nickname: string
  email: string
  avatar: string
  bio: string
}

const formRef = ref<FormInstance>()
const saving = ref(false)
const formData = ref<FormState>({
  username: '',
  nickname: '',
  email: '',
  avatar: '',
  bio: ''
})

// 激活的个人资料标签页
const activeProfileTab = ref('info')

const rules: FormRules = {
  nickname: [
    {
      required: true,
      message: '请输入昵称',
      trigger: 'blur'
    }
  ],
  email: [
    {
      required: true,
      message: '请输入邮箱',
      trigger: 'blur'
    },
    {
      type: 'email',
      message: '请输入有效的邮箱地址',
      trigger: ['blur', 'input']
    }
  ]
}

// 密码修改相关
const passwordFormRef = ref<FormInstance>()
const changingPassword = ref(false)
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const validateConfirmPassword = (_: any, value: string) => {
  if (!value) {
    return new Error('请再次输入新密码')
  }
  if (value !== passwordForm.value.newPassword) {
    return new Error('两次输入的密码不一致')
  }
  return true
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 文章相关
const loadingArticles = ref(false)
const articles = ref<Article[]>([])
const pagination: PaginationProps = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
  onChange: (page: number) => {
    pagination.page = page
    fetchArticles()
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize
    fetchArticles()
  }
}

const articleColumns: DataTableColumns<Article> = [
  {
    title: '标题',
    key: 'title',
    render: (row: Article) => {
      return h(
        RouterLink,
        {
          to: `/articles/${row._id}`
        },
        { default: () => row.title }
      )
    }
  },
  {
    title: '状态',
    key: 'status',
    render: (row: Article) => {
      return h(
        NTag,
        {
          type: row.status === 'published' ? 'success' : 'warning'
        },
        { default: () => row.status === 'published' ? '已发布' : '草稿' }
      )
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    render: (row: Article) => formatDate(row.created_at)
  },
  {
    title: '操作',
    key: 'actions',
    render: (row: Article) => {
      return h(
        NSpace,
        { justify: 'center' },
        {
          default: () => [
            h(
              RouterLink,
              {
                to: `/articles/edit/${row._id}`
              },
              { default: () => h(NButton, { text: true }, { default: () => '编辑' }) }
            ),
            h(
              NPopconfirm,
              {
                onPositiveClick: () => handleDelete(row._id)
              },
              {
                default: () => '确认删除这篇文章吗？此操作不可恢复。',
                trigger: () =>
                  h(NButton, { text: true, type: 'error' }, { default: () => '删除' })
              }
            )
          ]
        }
      )
    }
  }
]

// 调试开关
const showDebugInfo = ref(true)

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const res = await userApi.getProfile()
    console.log('获取用户信息响应:', res)
    if (res.data.code === 0 || res.data.code === 200) {
      const userData = res.data.data.user
      console.log('用户数据:', userData)
      formData.value = {
        username: userData.username || '',
        nickname: userData.nickname || '',
        email: userData.email || '',
        avatar: userData.avatar || '',
        bio: userData.bio || ''
      }
      console.log('更新后的表单数据:', formData.value)
    } else {
      message.error(res.data.message || '获取用户信息失败')
    }
  } catch (error: any) {
    console.error('获取用户信息错误:', error)
    message.error(error.response?.data?.message || '获取用户信息失败')
  }
}

// 获取文章列表
const fetchArticles = async () => {
  try {
    loadingArticles.value = true
    const { data } = await articleApi.getArticles({
      page: pagination.page,
      limit: pagination.pageSize,
      author: 'me',
      status: activeTab.value
    })
    if (data.code === 0 || data.code === 200) {
      articles.value = data.data.items || []
      pagination.itemCount = data.data.total || 0
    }
  } catch (error: any) {
    message.error(error.response?.data?.message || '获取文章列表失败')
  } finally {
    loadingArticles.value = false
  }
}

// 保存用户信息
const handleSave = () => {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        saving.value = true
        const { username, ...updateData } = formData.value
        const { data } = await userApi.updateProfile(updateData)
        if (data.code === 0 || data.code === 200) {
          message.success('保存成功')
          // 更新用户 store 中的信息
          const userStore = useUserStore()
          await userStore.fetchUserInfo()
        } else {
          message.error(data.message || '保存失败')
        }
      } catch (error: any) {
        message.error(error.response?.data?.message || '保存失败')
      } finally {
        saving.value = false
      }
    }
  })
}

// 修改密码
const handleChangePassword = () => {
  passwordFormRef.value?.validate(async (errors: any) => {
    if (!errors) {
      try {
        changingPassword.value = true
        const { confirmPassword, ...passwordData } = passwordForm.value
        const { data } = await userApi.changePassword(passwordData)
        if (data.code === 0 || data.code === 200) {
          message.success('密码修改成功')
          passwordForm.value = {
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
          }
        } else {
          message.error(data.message || '密码修改失败')
        }
      } catch (error: any) {
        message.error(error.response?.data?.message || '密码修改失败')
      } finally {
        changingPassword.value = false
      }
    }
  })
}

// 编辑文章
const handleEditArticle = (id: string) => {
  router.push(`/articles/edit/${id}`)
}

// 删除文章
const handleDelete = async (id: string) => {
  try {
    await dialog.warning({
      title: '确认删除',
      content: '确定要删除这篇文章吗？此操作不可恢复。',
      positiveText: '确定',
      negativeText: '取消'
    })
    const { data } = await articleApi.deleteArticle(id)
    if (data.code === 0) {
      message.success('删除成功')
      fetchArticles()
    }
  } catch (error: any) {
    if (error) {
      message.error(error.response?.data?.message || '删除失败')
    }
  }
}

// 处理头像上传
const handleAvatarUpload = async (options: any) => {
  try {
    console.log('===== 头像上传流程开始 =====')
    console.log('上传文件信息:', options.file)
    
    const { file } = options
    const uploadFormData = new FormData()
    uploadFormData.append('image', file.file)
    
    console.log('开始上传头像...')
    const { data } = await uploadApi.uploadImage(uploadFormData)
    console.log('上传头像响应:', data)
    
    if (data.code === 0 || data.code === 200) {
      // 确保头像URL是完整的
      const baseUrl = UPLOADS_BASE_URL || ''
      const avatarUrl = data.data.url
      
      console.log('服务器返回的头像URL:', data.data.url)
      console.log('处理后的头像URL:', avatarUrl)
      
      // 测试图片是否可访问
      console.log('测试图片是否可访问...')
      const testImg = new Image()
      testImg.onload = () => console.log('测试图片加载成功!')
      testImg.onerror = (e) => console.error('测试图片加载失败:', e)
      testImg.src = baseUrl + avatarUrl
      
      console.log('开始更新用户资料...')
      // 更新头像
      const updateRes = await userApi.updateProfile({
        nickname: formData.value.nickname,
        email: formData.value.email,
        bio: formData.value.bio,
        avatar: avatarUrl  // 使用原始URL，让后端保存
      })
      
      if (updateRes.data.code === 0 || updateRes.data.code === 200) {
        console.log('用户资料更新成功，头像URL:', avatarUrl)
        
        // 添加时间戳避免缓存问题
        const finalAvatarUrl = avatarUrl + '?t=' + new Date().getTime()
        formData.value.avatar = finalAvatarUrl
        console.log('更新本地表单数据头像URL:', finalAvatarUrl)
        
        message.success('头像更新成功')
        
        // 更新用户 store 中的信息
        console.log('更新用户store...')
        const userStore = useUserStore()
        await userStore.fetchUserInfo()
        console.log('用户store更新完成，新头像:', userStore.user?.avatar)
        
        // 强制刷新页面上的所有头像
        setTimeout(() => {
          const avatarElements = document.querySelectorAll('.n-avatar img')
          console.log('找到头像元素数量:', avatarElements.length)
          avatarElements.forEach(img => {
            const src = img.getAttribute('src')
            if(src) {
              const newSrc = avatarUrl + '?t=' + new Date().getTime()
              console.log(`更新头像元素，原URL: ${src}, 新URL: ${newSrc}`)
              img.setAttribute('src', newSrc)
            }
          })
        }, 500)
      } else {
        message.error(updateRes.data.message || '头像更新失败')
      }
    } else {
      message.error(data.message || '图片上传失败')
    }
  } catch (error: any) {
    console.error('头像上传错误:', error)
    message.error(error.response?.data?.message || '头像上传失败')
  }
}

// 监听标签页切换
const activeTab = ref('published')
const handleTabChange = (name: string) => {
  activeTab.value = name
  pagination.page = 1
  fetchArticles()
}

// 监听分页变化
watch(
  () => [pagination.page, pagination.pageSize],
  () => {
    fetchArticles()
  }
)

// 处理头像加载错误
const handleAvatarError = (e: Event) => {
  console.error('头像加载失败:', e)
  console.error('当前头像URL:', formData.value.avatar)
  message.error('头像加载失败，请检查图片URL')
}

// 修复头像
const fixAvatar = async () => {
  try {
    console.log('开始修复头像...')
    message.info('正在修复头像...')
    
    // 设置默认头像
    const defaultAvatar = '/uploads/touxiang.png'
    
    // 更新用户头像
    const updateRes = await userApi.updateProfile({
      nickname: formData.value.nickname,
      email: formData.value.email,
      bio: formData.value.bio,
      avatar: defaultAvatar
    })
    
    if (updateRes.data.code === 0 || updateRes.data.code === 200) {
      // 添加时间戳避免缓存问题
      formData.value.avatar = defaultAvatar + '?t=' + new Date().getTime()
      message.success('头像已重置为默认头像')
      
      // 更新用户 store 中的信息
      const userStore = useUserStore()
      await userStore.fetchUserInfo()
      
      // 强制刷新页面上的所有头像
      setTimeout(() => {
        const avatarElements = document.querySelectorAll('.n-avatar img')
        console.log('找到头像元素数量:', avatarElements.length)
        avatarElements.forEach(img => {
          const src = img.getAttribute('src')
          if(src) {
            img.setAttribute('src', defaultAvatar + '?t=' + new Date().getTime())
          }
        })
      }, 500)
    } else {
      message.error(updateRes.data.message || '头像修复失败')
    }
  } catch (error: any) {
    console.error('头像修复错误:', error)
    message.error(error.response?.data?.message || '头像修复失败')
  }
}

onMounted(async () => {
  await fetchUserInfo()
  fetchArticles()
})
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f6f9fc 0%, #eef1f5 100%);
  padding: 2rem 0;

  .page-header {
    background: linear-gradient(120deg, #3a7bd5, #00d2ff);
    padding: 3rem 0;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
      opacity: 0.3;
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
      position: relative;
      z-index: 1;
      text-align: center;

      h1 {
        font-size: 3rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .subtitle {
        font-size: 1.25rem;
        margin-top: 1rem;
        font-weight: 400;
        opacity: 0.9;
      }
    }
  }

  .content-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;

    .profile-layout {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: 2rem;
      
      .profile-sidebar {
        .user-card {
          background: white;
          border-radius: 16px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
          padding: 2rem;
          text-align: center;
          position: relative;
          
          .avatar-wrapper {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto 1.5rem;
            
            .user-avatar {
              border: 4px solid white;
              box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
              background: linear-gradient(120deg, #3a7bd5, #00d2ff);
              font-size: 2.5rem;
              font-weight: 600;
            }
            
            .avatar-edit-badge {
              position: absolute;
              bottom: 0;
              right: 0;
              background: white;
              border-radius: 50%;
              padding: 4px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
          }
          
          .user-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0 0 0.5rem;
            color: #2c3e50;
          }
          
          .user-bio {
            color: #7f8c8d;
            margin: 0 0 1.5rem;
            font-size: 0.95rem;
            line-height: 1.5;
          }
          
          .user-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            padding: 1.5rem 0;
            border-top: 1px solid #f1f1f1;
            
            .stat-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              
              .stat-value {
                font-size: 1.5rem;
                font-weight: 600;
                color: #3a7bd5;
              }
              
              .stat-label {
                font-size: 0.875rem;
                color: #7f8c8d;
                margin-top: 0.25rem;
              }
            }
          }
        }
      }
      
      .profile-content {
        .profile-tabs {
          margin-bottom: 1.5rem;
          
          :deep(.n-tabs-nav) {
            background: white;
            padding: 0.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
          }
        }
        
        .section-card {
          background: white;
          border-radius: 16px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
          overflow: hidden;
          
          .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem;
            border-bottom: 1px solid #f1f1f1;
            
            h2 {
              font-size: 1.25rem;
              margin: 0;
              color: #2c3e50;
              font-weight: 600;
            }
          }
          
          :deep(.n-form) {
            padding: 1.5rem;
            
            .n-form-item-label {
              font-weight: 500;
              color: #2c3e50;
            }
            
            .n-input {
              border-radius: 8px;
              
              &:hover, &:focus {
                box-shadow: 0 0 0 2px rgba(58, 123, 213, 0.1);
              }
            }
          }
          
          .form-actions {
            margin-top: 2rem;
            padding: 1rem 1.5rem;
            background: #f8fafc;
            border-top: 1px solid #f1f1f1;
            display: flex;
            justify-content: flex-end;
          }
          
          .article-tabs {
            :deep(.n-tabs-nav) {
              padding: 0 1.5rem;
            }
            
            :deep(.n-tab-pane) {
              padding: 1rem 1.5rem;
            }
            
            :deep(.n-tabs-bar) {
              background-color: #3a7bd5;
            }
          }
          
          .article-table {
            :deep(.n-data-table-th) {
              background: #f8fafc;
            }
            
            :deep(.article-row) {
              transition: background-color 0.2s;
              
              &:hover {
                background-color: #f8fafc;
              }
            }
            
            :deep(.n-button) {
              border-radius: 6px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .profile-page {
    .content-wrapper {
      .profile-layout {
        grid-template-columns: 1fr;
      }
    }
  }
}

@media (max-width: 768px) {
  .profile-page {
    padding: 0;
    
    .page-header {
      padding: 2rem 0;
      margin-bottom: 2rem;
      
      .header-content {
        h1 {
          font-size: 2.25rem;
        }
        
        .subtitle {
          font-size: 1rem;
        }
      }
    }
    
    .content-wrapper {
      padding: 0 1rem;
      
      .profile-layout {
        gap: 1.5rem;
        
        .section-card {
          border-radius: 12px;
          
          .section-header {
            padding: 1.25rem;
          }
          
          :deep(.n-form) {
            padding: 1.25rem;
          }
          
          .form-actions {
            padding: 1rem 1.25rem;
          }
        }
      }
    }
  }
}
</style> 