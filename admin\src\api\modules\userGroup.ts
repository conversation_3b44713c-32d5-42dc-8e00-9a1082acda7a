import request from '@/utils/request'

interface User {
  id: number;
  username: string;
  email: string;
  nickname?: string;
  avatar?: string;
}

interface UserGroup {
  id: number;
  name: string;
  description?: string;
  members: User[];
  created_at: string;
  updated_at: string;
}

interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

interface UserGroupsResponse {
  total: number;
  items: UserGroup[];
  page: number;
  pageSize: number;
  totalPages: number;
}

interface UserGroupResponse {
  userGroup: UserGroup;
}

const userGroupApi = {
  // 获取所有用户组
  getUserGroups() {
    return request.get<ApiResponse<UserGroupsResponse>>('/user-groups', {
      params: {
        isUser: false // 表明是管理后台请求
      }
    });
  },

  // 获取单个用户组详情
  getUserGroup(id: string) {
    return request.get<ApiResponse<UserGroup>>(`/user-groups/${id}`, {
      params: {
        isUser: false
      }
    });
  },

  // 创建用户组
  createUserGroup(data: {
    name: string;
    description?: string;
    members?: string[];
  }) {
    return request.post<ApiResponse<UserGroup>>('/user-groups', data);
  },

  // 更新用户组
  updateUserGroup(
    id: string,
    data: {
      name?: string;
      description?: string;
    }
  ) {
    return request.patch<ApiResponse<UserGroup>>(`/user-groups/${id}`, data);
  },

  // 删除用户组
  deleteUserGroup(id: string) {
    return request.delete<ApiResponse<null>>(`/user-groups/${id}`);
  },

  // 添加用户到用户组
  addUserToGroup(groupId: string, userId: string) {
    return request.patch<ApiResponse<UserGroup>>(`/user-groups/${groupId}/add-user`, { userId });
  },

  // 从用户组移除用户
  removeUserFromGroup(groupId: string, userId: string) {
    return request.patch<ApiResponse<UserGroup>>(`/user-groups/${groupId}/remove-user`, { userId });
  }
};

export default userGroupApi;
