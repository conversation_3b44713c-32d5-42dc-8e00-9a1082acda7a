"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const sequelize_1 = require("sequelize");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const db_1 = require("../config/db");
class User extends sequelize_1.Model {
    // 比较密码方法
    async comparePassword(candidatePassword) {
        try {
            return await bcryptjs_1.default.compare(candidatePassword, this.password);
        }
        catch (error) {
            return false;
        }
    }
}
exports.User = User;
User.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    },
    username: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        validate: {
            notEmpty: { msg: '用户名是必需的' },
            len: { args: [1, 50], msg: '用户名长度必须在1-50个字符之间' }
        }
    },
    email: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
            isEmail: { msg: '请输入有效的邮箱地址' },
            notEmpty: { msg: '邮箱是必需的' }
        }
    },
    password: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: false,
        validate: {
            notEmpty: { msg: '密码是必需的' },
            len: { args: [6, 255], msg: '密码长度至少6个字符' }
        }
    },
    role: {
        type: sequelize_1.DataTypes.ENUM('user', 'admin'),
        defaultValue: 'user'
    },
    nickname: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: true,
        validate: {
            len: { args: [0, 50], msg: '昵称不能超过50个字符' }
        }
    },
    bio: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        validate: {
            len: { args: [0, 500], msg: '个人简介不能超过500个字符' }
        }
    },
    avatar: {
        type: sequelize_1.DataTypes.STRING(255),
        defaultValue: '/uploads/touxiang.png'
    },
    is_active: {
        type: sequelize_1.DataTypes.BOOLEAN,
        defaultValue: true
    },
    last_login: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        defaultValue: sequelize_1.DataTypes.NOW
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        defaultValue: sequelize_1.DataTypes.NOW
    }
}, {
    sequelize: db_1.sequelize,
    modelName: 'User',
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    hooks: {
        beforeCreate: async (instance) => {
            if (instance.password) {
                const salt = await bcryptjs_1.default.genSalt(12);
                instance.password = await bcryptjs_1.default.hash(instance.password, salt);
            }
        },
        beforeUpdate: async (instance) => {
            if (instance.changed('password')) {
                const salt = await bcryptjs_1.default.genSalt(12);
                instance.password = await bcryptjs_1.default.hash(instance.password, salt);
            }
        }
    }
});
exports.default = User;
