"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCategoryBySlug = exports.deleteCategory = exports.updateCategory = exports.createCategory = exports.getCategory = exports.getCategories = void 0;
const express_validator_1 = require("express-validator");
const Category_1 = __importDefault(require("../models/Category"));
const Article_1 = __importDefault(require("../models/Article"));
// @desc    获取所有分类
// @route   GET /api/categories
// @access  Public
const getCategories = async (req, res) => {
    try {
        // 获取所有分类
        const categories = await Category_1.default.findAll({
            order: [['name', 'ASC']]
        });
        // 获取每个分类的文章数量
        const categoriesWithCount = await Promise.all(categories.map(async (category) => {
            const count = await Article_1.default.count({
                where: {
                    category_id: category.id,
                    status: 'published' // 只统计已发布的文章
                }
            });
            return {
                ...category.toJSON(),
                article_count: count
            };
        }));
        res.json({
            code: 0,
            message: '获取分类列表成功',
            data: categoriesWithCount
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '获取分类列表失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.getCategories = getCategories;
// @desc    获取单个分类
// @route   GET /api/categories/:id
// @access  Public
const getCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const category = await Category_1.default.findByPk(id);
        if (!category) {
            return res.status(404).json({
                code: 404,
                message: '分类不存在'
            });
        }
        res.json({
            code: 0,
            message: '获取分类成功',
            data: category
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '获取分类失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.getCategory = getCategory;
// @desc    创建分类
// @route   POST /api/categories
// @access  Private/Admin
const createCategory = async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                code: 400,
                message: '输入数据验证失败',
                errors: errors.array()
            });
        }
        const { name, description, slug } = req.body;
        // 检查分类名是否已存在
        const existingCategory = await Category_1.default.findOne({
            where: { name }
        });
        if (existingCategory) {
            return res.status(400).json({
                code: 400,
                message: '分类名称已存在'
            });
        }
        // 生成或验证slug
        let finalSlug = slug || name.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, '-');
        // 检查slug是否已存在
        let slugExists = await Category_1.default.findOne({
            where: { slug: finalSlug }
        });
        let counter = 1;
        while (slugExists) {
            finalSlug = `${slug || name.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, '-')}-${counter}`;
            slugExists = await Category_1.default.findOne({
                where: { slug: finalSlug }
            });
            counter++;
        }
        const category = await Category_1.default.create({
            name,
            description,
            slug: finalSlug
        });
        res.status(201).json({
            code: 201,
            message: '创建分类成功',
            data: category
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '创建分类失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.createCategory = createCategory;
// @desc    更新分类
// @route   PUT /api/categories/:id
// @access  Private/Admin
const updateCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const updates = req.body;
        const [affectedCount] = await Category_1.default.update(updates, {
            where: { id }
        });
        if (affectedCount === 0) {
            return res.status(404).json({
                code: 404,
                message: '分类不存在'
            });
        }
        const updatedCategory = await Category_1.default.findByPk(id);
        res.json({
            code: 0,
            message: '更新分类成功',
            data: updatedCategory
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '更新分类失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.updateCategory = updateCategory;
// @desc    删除分类
// @route   DELETE /api/categories/:id
// @access  Private/Admin
const deleteCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const category = await Category_1.default.findByPk(id);
        if (!category) {
            return res.status(404).json({
                code: 404,
                message: '分类不存在'
            });
        }
        // 检查是否有文章使用此分类
        const articleCount = await Article_1.default.count({
            where: { category_id: id }
        });
        if (articleCount > 0) {
            return res.status(400).json({
                code: 400,
                message: `无法删除分类，还有 ${articleCount} 篇文章使用此分类`
            });
        }
        await category.destroy();
        res.json({
            code: 0,
            message: '删除分类成功'
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '删除分类失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.deleteCategory = deleteCategory;
// @desc    根据slug获取分类
// @route   GET /api/categories/slug/:slug
// @access  Public
const getCategoryBySlug = async (req, res) => {
    try {
        const { slug } = req.params;
        const category = await Category_1.default.findOne({
            where: { slug }
        });
        if (!category) {
            return res.status(404).json({
                code: 404,
                message: '分类不存在'
            });
        }
        res.json({
            code: 0,
            message: '获取分类成功',
            data: category
        });
    }
    catch (error) {
        res.status(500).json({
            code: 500,
            message: '获取分类失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
exports.getCategoryBySlug = getCategoryBySlug;
