"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeAnnouncements = exports.deleteAnnouncement = exports.updateAnnouncement = exports.createAnnouncement = exports.getAnnouncements = void 0;
const Announcement_1 = __importDefault(require("../models/Announcement"));
const catchAsync_1 = require("../utils/catchAsync");
const appError_1 = __importDefault(require("../utils/appError"));
// 获取公告列表
exports.getAnnouncements = (0, catchAsync_1.catchAsync)(async (req, res) => {
    console.log('Getting announcements...');
    const announcements = await Announcement_1.default.findAll({
        order: [['created_at', 'DESC']]
    });
    console.log(`Found ${announcements.length} announcements`);
    res.status(200).json({
        code: 200,
        message: '获取公告列表成功',
        data: announcements
    });
});
// 创建公告
exports.createAnnouncement = (0, catchAsync_1.catchAsync)(async (req, res) => {
    console.log('Creating announcement:', req.body);
    const announcement = await Announcement_1.default.create(req.body);
    res.status(201).json({
        code: 200,
        message: '创建公告成功',
        data: announcement
    });
});
// 更新公告
exports.updateAnnouncement = (0, catchAsync_1.catchAsync)(async (req, res) => {
    console.log('Updating announcement:', req.params.id, req.body);
    const [affectedCount] = await Announcement_1.default.update(req.body, {
        where: { id: req.params.id }
    });
    if (affectedCount === 0) {
        throw new appError_1.default('公告不存在', 404);
    }
    const announcement = await Announcement_1.default.findByPk(req.params.id);
    if (!announcement) {
        throw new appError_1.default('未找到该公告', 404);
    }
    res.status(200).json({
        code: 200,
        message: '更新公告成功',
        data: announcement
    });
});
// 删除公告
exports.deleteAnnouncement = (0, catchAsync_1.catchAsync)(async (req, res) => {
    console.log('Deleting announcement:', req.params.id);
    const announcement = await Announcement_1.default.findByPk(req.params.id);
    if (!announcement) {
        throw new appError_1.default('未找到该公告', 404);
    }
    await announcement.destroy();
    res.status(200).json({
        code: 200,
        message: '删除公告成功'
    });
});
// 初始化测试数据
exports.initializeAnnouncements = (0, catchAsync_1.catchAsync)(async (req, res) => {
    console.log('Initializing test announcements...');
    const testAnnouncements = [
        {
            title: '系统升级通知',
            content: '系统将于本周六凌晨2点进行升级维护，预计持续2小时。',
            type: 'info',
            is_active: true
        },
        {
            title: '新功能上线',
            content: '评论功能已经上线，欢迎大家使用！',
            type: 'success',
            is_active: true
        }
    ];
    console.log('Clearing existing announcements...');
    await Announcement_1.default.destroy({ where: {} }); // 清除现有数据
    console.log('Inserting test announcements...');
    await Announcement_1.default.bulkCreate(testAnnouncements);
    console.log('Test announcements initialized successfully');
    res.status(200).json({
        code: 200,
        message: '测试公告初始化成功'
    });
});
