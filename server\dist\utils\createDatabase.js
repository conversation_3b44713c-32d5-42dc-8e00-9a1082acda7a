"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const promise_1 = __importDefault(require("mysql2/promise"));
dotenv_1.default.config();
async function createDatabase() {
    try {
        console.log('开始创建数据库...');
        // 连接到MySQL服务器（不指定数据库）
        const connection = await promise_1.default.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '3306'),
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
        });
        console.log('MySQL连接成功');
        // 创建数据库
        const dbName = process.env.DB_NAME || 'admin_system';
        await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
        console.log(`数据库 ${dbName} 创建成功`);
        // 关闭连接
        await connection.end();
        console.log('数据库连接已关闭');
        process.exit(0);
    }
    catch (error) {
        console.error('创建数据库失败:', error);
        process.exit(1);
    }
}
createDatabase();
