<template>
  <div class="tags-page">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <h1>文章标签</h1>
        <p class="subtitle">按兴趣浏览不同主题的文章</p>
      </div>
    </div>

    <div class="tags-container">
      <!-- 标签云卡片 -->
      <n-card :bordered="false" class="tags-card">
        <div class="tags-cloud">
          <n-space :size="20" justify="center" align="center" wrap>
            <n-tag
              v-for="tag in tags"
              :key="tag._id"
              class="tag-item"
              :type="getRandomType()"
              :bordered="false"
              size="large"
              round
              @click="handleTagClick(tag)"
            >
              <template #icon>
                <n-icon><PricetagOutline /></n-icon>
              </template>
              <div class="tag-content">
                <span class="tag-name">{{ tag.name }}</span>
                <span class="tag-count">{{ tag.article_count || 0 }}</span>
              </div>
            </n-tag>
          </n-space>
        </div>

        <!-- 空状态 -->
        <n-empty 
          v-if="tags.length === 0" 
          description="暂无标签"
          size="large"
        >
          <template #icon>
            <n-icon size="48" depth="3">
              <PricetagsOutline />
            </n-icon>
          </template>
        </n-empty>
      </n-card>

      <!-- 热门标签统计 -->
      <n-card :bordered="false" class="stats-card" v-if="tags.length > 0">
        <template #header>
          <div class="stats-header">
            <n-icon size="24"><StatsChartOutline /></n-icon>
            <span>热门标签</span>
          </div>
        </template>
        <div class="stats-content">
          <n-list>
            <n-list-item v-for="(tag, index) in topTags" :key="tag._id">
              <div class="stats-list-item">
                <div class="rank-badge">{{ index + 1 }}</div>
                <n-thing>
                  <template #header>
                    <div class="tag-header">
                      <n-tag :type="getRandomType()" round size="small" class="stats-tag">
                        {{ tag.name }}
                      </n-tag>
                      <span class="stats-count">{{ tag.article_count || 0 }} 篇文章</span>
                    </div>
                  </template>
                  <template #description>
                    <div class="progress-wrapper">
                      <n-progress
                        type="line"
                        :percentage="getPercentage(tag.article_count)"
                        :height="8"
                        :border-radius="4"
                        :color="getProgressColor()"
                        :indicator-placement="'inside'"
                        :indicator-text-color="'#fff'"
                        :rail-color="'rgba(0, 0, 0, 0.04)'"
                      />
                      <span class="percentage-text">{{ getPercentage(tag.article_count) }}%</span>
                    </div>
                  </template>
                </n-thing>
              </div>
            </n-list-item>
          </n-list>
        </div>
      </n-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { tagApi } from '@/api'
import {
  PricetagOutline,
  PricetagsOutline,
  StatsChartOutline
} from '@vicons/ionicons5'
import {
  NCard,
  NSpace,
  NTag,
  NIcon,
  NEmpty,
  NList,
  NListItem,
  NThing,
  NProgress
} from 'naive-ui'

const router = useRouter()
const tags = ref<any[]>([])

// 标签类型列表
const tagTypes = ['default', 'primary', 'info', 'success', 'warning', 'error'] as const;
type TagType = typeof tagTypes[number];

// 随机获取标签类型
const getRandomType = (): TagType => {
  const index = Math.floor(Math.random() * tagTypes.length);
  return tagTypes[index];
}

// 获取进度条颜色
const getProgressColor = () => {
  const colors = ['#3b82f6', '#10b981', '#6366f1', '#8b5cf6', '#ec4899', '#f43f5e', '#0ea5e9']
  return colors[Math.floor(Math.random() * colors.length)]
}

// 获取百分比
const getPercentage = (count: number) => {
  const max = Math.max(...tags.value.map(t => t.article_count || 0))
  return max ? Math.round((count / max) * 100) : 0
}

// 获取热门标签（前5个）
const topTags = computed(() => {
  return [...tags.value]
    .sort((a, b) => (b.article_count || 0) - (a.article_count || 0))
    .slice(0, 5)
})

// 加载标签列表
const fetchTags = async () => {
  try {
    const res = await tagApi.getTags()
    console.log('获取到的标签响应:', res)
    if (res.data.code === 0) {
      // 确保数据正确映射，兼容MongoDB的_id和MySQL的id
      tags.value = res.data.data.map(tag => ({
        ...tag,
        id: tag.id || tag._id,
        article_count: tag.article_count || 0
      }))
      console.log('处理后的标签数据:', tags.value)
    }
  } catch (error) {
    console.error('获取标签列表失败:', error)
  }
}

// 处理标签点击
const handleTagClick = (tag: any) => {
  router.push({
    path: '/articles',
    query: { tag: tag.id }
  })
}

onMounted(() => {
  fetchTags()
})
</script>

<style lang="scss" scoped>
.tags-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem 0;
}

.page-header {
  background: linear-gradient(135deg, #00B4DB, #0083B0);
  padding: 4rem 2rem;
  margin-bottom: 3rem;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
    opacity: 0.6;
  }

  .header-content {
    position: relative;
    z-index: 1;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 1rem;
      background: linear-gradient(120deg, #fff, #e0f7fa);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .subtitle {
      font-size: 1.2rem;
      opacity: 0.9;
      margin: 0;
    }
  }
}

.tags-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.tags-card,
.stats-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  }
}

.tags-cloud {
  padding: 2rem;

  .tag-item {
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    padding: 0.5rem 1rem;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .tag-content {
      display: flex;
      align-items: center;
      gap: 8px;

      .tag-name {
        font-weight: 500;
      }

      .tag-count {
        font-size: 0.85rem;
        opacity: 0.8;
        background: rgba(255, 255, 255, 0.2);
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 24px;
        text-align: center;
      }
    }
  }
}

.stats-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 14px 36px rgba(0, 0, 0, 0.12);
  }

  .stats-header {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #1a202c;
    font-size: 1.2rem;
    font-weight: 600;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: linear-gradient(to right, rgba(255,255,255,0.8), rgba(240,249,255,0.8));
  }

  .stats-content {
    padding: 0.5rem;
  }

  :deep(.n-list) {
    background: transparent;
  }

  :deep(.n-list-item) {
    padding: 0.75rem;
    transition: all 0.2s ease;
    border-radius: 12px;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
      transform: translateX(4px);
    }
  }

  .stats-list-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
  }

  .rank-badge {
    background: linear-gradient(135deg, #e0f2fe, #bae6fd);
    color: #0369a1;
    font-weight: 700;
    font-size: 0.9rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-shrink: 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .tag-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 6px;
  }

  .stats-tag {
    font-weight: 500;
    padding: 0 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .stats-count {
    font-size: 0.9rem;
    color: #4b5563;
    font-weight: 500;
  }

  .progress-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 0.5rem;
    width: 100%;
  }

  .percentage-text {
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 500;
    min-width: 36px;
  }

  :deep(.n-progress) {
    flex-grow: 1;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .tags-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 3rem 1rem;

    h1 {
      font-size: 2rem;
    }

    .subtitle {
      font-size: 1rem;
    }
  }

  .tags-container {
    padding: 0 1rem;
  }

  .tags-cloud {
    padding: 1rem;
  }
}

// 暗色主题适配
:deep(.dark) {
  .tags-card,
  .stats-card {
    background: rgba(30, 41, 59, 0.7);

    .tag-item {
      .tag-content {
        .tag-name {
          color: #e5eaf3;
        }

        .tag-count {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(229, 234, 243, 0.8);
        }
      }
    }
  }

  .stats-card {
    .stats-header {
      color: #f1f5f9;
      border-bottom-color: rgba(255, 255, 255, 0.1);
      background: linear-gradient(to right, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.8));
    }

    .rank-badge {
      background: linear-gradient(135deg, #1e293b, #334155);
      color: #94a3b8;
    }

    :deep(.n-list-item) {
      &:hover {
        background-color: rgba(255, 255, 255, 0.05);
      }
    }

    .stats-count {
      color: #cbd5e1;
    }

    .percentage-text {
      color: #94a3b8;
    }

    :deep(.n-progress-rail) {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }
  }
}
</style>