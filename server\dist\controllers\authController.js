"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.register = exports.changePassword = exports.getMe = exports.login = void 0;
const express_validator_1 = require("express-validator");
const sequelize_1 = require("sequelize");
const User_1 = __importDefault(require("../models/User"));
const Setting_1 = __importDefault(require("../models/Setting"));
const error_1 = require("../middleware/error");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const logger_1 = __importDefault(require("../utils/logger"));
// @desc    用户登录
// @route   POST /api/auth/login
// @access  Public
const login = async (req, res, next) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                code: 400,
                message: '验证错误',
                errors: errors.array()
            });
        }
        const { account, password } = req.body;
        console.log('登录请求:', { account });
        // 检查用户是否存在
        const user = await User_1.default.findOne({
            where: {
                [sequelize_1.Op.or]: [
                    { email: account },
                    { username: account }
                ]
            }
        });
        if (!user) {
            console.log('登录失败: 用户不存在', { account });
            return next(new error_1.ErrorResponse('无效的凭据', 401));
        }
        // 检查密码是否匹配
        const isMatch = await user.comparePassword(password);
        if (!isMatch) {
            console.log('登录失败: 密码不匹配', { account });
            return next(new error_1.ErrorResponse('无效的凭据', 401));
        }
        // 检查用户状态
        if (!user.is_active) {
            console.log('登录失败: 用户被禁用', { account });
            return next(new error_1.ErrorResponse('您的账户已被禁用，请联系管理员', 403));
        }
        // 生成JWT Token
        const jwtSecret = process.env.JWT_SECRET || 'blogadminsecretkey2024';
        const jwtExpires = process.env.JWT_EXPIRES_IN || '7d';
        console.log('生成令牌，使用密钥:', jwtSecret.substring(0, 3) + '...');
        console.log('令牌过期时间:', jwtExpires);
        const token = jsonwebtoken_1.default.sign({ id: user.id }, jwtSecret, { expiresIn: jwtExpires });
        // 更新最后登录时间
        user.last_login = new Date();
        await user.save();
        // 移除密码（从返回数据中）
        const userWithoutPassword = user.toJSON();
        delete userWithoutPassword.password;
        console.log('登录成功:', { username: user.username, role: user.role });
        // 将token设置为Cookie
        res.cookie('token', token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
        });
        res.status(200).json({
            code: 200,
            message: '登录成功',
            data: {
                token,
                user: {
                    id: userWithoutPassword.id,
                    username: userWithoutPassword.username,
                    email: userWithoutPassword.email,
                    role: userWithoutPassword.role,
                    avatar: userWithoutPassword.avatar
                }
            }
        });
    }
    catch (error) {
        console.error('登录过程发生错误:', error);
        next(error);
    }
};
exports.login = login;
// @desc    获取当前登录用户信息
// @route   GET /api/auth/me
// @access  Private
const getMe = async (req, res, next) => {
    try {
        const user = req.user;
        console.log('获取用户信息 - 用户数据:', user);
        res.status(200).json({
            code: 200,
            message: '获取用户信息成功',
            data: {
                user: {
                    id: user?._id,
                    username: user?.username,
                    email: user?.email,
                    role: user?.role,
                    nickname: user?.nickname,
                    bio: user?.bio,
                    avatar: user?.avatar,
                    status: user?.status,
                    avatar_color: user?.avatar_color,
                    created_at: user?.created_at,
                    updated_at: user?.updated_at
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getMe = getMe;
// @desc    修改密码
// @route   PUT /api/auth/change-password
// @access  Private
const changePassword = async (req, res, next) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                code: 400,
                message: '验证错误',
                errors: errors.array()
            });
        }
        const { currentPassword, newPassword } = req.body;
        // 获取用户
        const user = await User_1.default.findByPk(req.user?.id);
        if (!user) {
            return next(new error_1.ErrorResponse('用户不存在', 404));
        }
        // 检查当前密码是否正确
        const isMatch = await user.comparePassword(currentPassword);
        if (!isMatch) {
            return next(new error_1.ErrorResponse('当前密码不正确', 401));
        }
        // 更新密码
        user.password = newPassword;
        await user.save();
        res.status(200).json({
            code: 200,
            message: '密码修改成功'
        });
    }
    catch (error) {
        next(error);
    }
};
exports.changePassword = changePassword;
// @desc    用户注册
// @route   POST /api/auth/register
// @access  Public
const register = async (req, res, next) => {
    try {
        console.log('注册请求开始:', req.body);
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            console.log('验证错误:', errors.array());
            return res.status(400).json({
                code: 400,
                message: '验证错误',
                errors: errors.array()
            });
        }
        const { username, email, password, nickname, inviteCode } = req.body;
        console.log('提取的字段:', { username, email, nickname, inviteCode });
        // 检查是否启用了注册功能
        console.log('检查注册设置...');
        const registrationEnabledSetting = await Setting_1.default.findOne({
            where: {
                group: 'registration',
                key: 'registrationEnabled'
            }
        });
        console.log('注册设置查询结果:', registrationEnabledSetting);
        if (registrationEnabledSetting && registrationEnabledSetting.value === false) {
            logger_1.default.warn('注册尝试失败: 注册功能已关闭');
            return next(new error_1.ErrorResponse('注册功能已关闭，请联系管理员', 403));
        }
        // 检查是否需要邀请码
        const inviteCodeRequiredSetting = await Setting_1.default.findOne({
            where: {
                group: 'registration',
                key: 'inviteCodeRequired'
            }
        });
        if (inviteCodeRequiredSetting && inviteCodeRequiredSetting.value === true) {
            // 如果需要邀请码
            if (!inviteCode) {
                logger_1.default.warn('注册尝试失败: 未提供邀请码');
                return next(new error_1.ErrorResponse('注册需要邀请码', 400));
            }
            // 获取系统中设置的邀请码
            const systemInviteCodeSetting = await Setting_1.default.findOne({
                where: {
                    group: 'registration',
                    key: 'inviteCode'
                }
            });
            if (!systemInviteCodeSetting || systemInviteCodeSetting.value !== inviteCode) {
                logger_1.default.warn('注册尝试失败: 邀请码不正确', { providedCode: inviteCode });
                return next(new error_1.ErrorResponse('邀请码不正确', 400));
            }
            logger_1.default.info('邀请码验证成功', { inviteCode });
        }
        // 检查用户名是否已存在
        const existingUsername = await User_1.default.findOne({ where: { username } });
        if (existingUsername) {
            return next(new error_1.ErrorResponse('用户名已被使用', 400));
        }
        // 检查邮箱是否已存在
        const existingEmail = await User_1.default.findOne({ where: { email } });
        if (existingEmail) {
            return next(new error_1.ErrorResponse('邮箱已被注册', 400));
        }
        // 创建新用户
        console.log('开始创建用户...');
        const user = await User_1.default.create({
            username,
            email,
            password,
            nickname: nickname || username, // 如果没有提供昵称，使用用户名作为默认昵称
            avatar: '/uploads/touxiang.png', // 默认头像
            is_active: true,
            role: 'user'
        });
        console.log('用户创建成功:', user.id);
        // 生成JWT Token
        const token = jsonwebtoken_1.default.sign({ id: user.id }, process.env.JWT_SECRET || 'blogadminsecretkey2024', { expiresIn: process.env.JWT_EXPIRES_IN || '7d' });
        // 移除密码
        user.password = undefined;
        logger_1.default.info('用户注册成功:', { username: user.username, email: user.email });
        res.status(201).json({
            code: 200,
            message: '注册成功',
            data: {
                token,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    nickname: user.nickname,
                    role: user.role,
                    avatar: user.avatar
                }
            }
        });
    }
    catch (error) {
        console.error('注册过程发生错误:', error);
        logger_1.default.error('注册过程发生错误:', error);
        // 如果是MongoDB重复键错误
        if (error.code === 11000) {
            const field = Object.keys(error.keyPattern || {})[0];
            const message = field === 'email' ? '邮箱已被注册' : '用户名已被使用';
            return next(new error_1.ErrorResponse(message, 400));
        }
        // 如果是验证错误
        if (error.name === 'ValidationError') {
            const messages = Object.values(error.errors || {}).map((err) => err.message);
            return next(new error_1.ErrorResponse(messages.join(', '), 400));
        }
        next(error);
    }
};
exports.register = register;
