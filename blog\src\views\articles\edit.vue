<template>
  <div class="article-edit">
    <div class="page-header">
      <h1>{{ isEdit ? '编辑文章' : '写文章' }}</h1>
      <p class="subtitle">分享你的想法和知识</p>
    </div>

    <div class="content-wrapper">
      <n-grid :cols="24" :x-gap="24">
        <!-- 左侧主要编辑区域 -->
        <n-grid-item :span="20">
          <n-card class="edit-card main-card" :bordered="false">
            <n-form
              ref="formRef"
              :model="formData"
              :rules="rules"
              label-placement="left"
              label-width="80"
              require-mark-placement="right-hanging"
            >
              <n-form-item label="标题" path="title">
                <n-input
                  v-model:value="formData.title"
                  placeholder="请输入文章标题"
                  clearable
                  :maxlength="100"
                  show-count
                />
              </n-form-item>

              <n-form-item label="内容" path="content">
                <div class="editor-wrapper">
                  <TinyEditor
                    v-model="formData.content"
                    :height="700"
                    :disabled="false"
                    placeholder="请输入文章内容..."
                    @editorInit="handleEditorInit"
                  />
                </div>
              </n-form-item>
            </n-form>
          </n-card>
        </n-grid-item>

        <!-- 右侧设置面板 -->
        <n-grid-item :span="4">
          <n-space :size="24" vertical>
            <!-- 发布设置 -->
            <n-card class="edit-card setting-card" :bordered="false" title="发布设置">
              <n-space vertical :size="16">
                <div class="setting-item">
                  <span class="setting-label">可见性</span>
                  <n-select
                    v-model:value="formData.visibility"
                    :options="visibilityOptions"
                    @update:value="handleVisibilityChange"
                  />
                </div>

                <div class="setting-item" v-if="formData.visibility === 'custom'">
                  <span class="setting-label">可见用户</span>
                  <n-select
                    v-model:value="formData.visible_to"
                    :options="userOptions"
                    multiple
                    filterable
                    placeholder="选择可见用户"
                  />
                </div>

                <div class="setting-item" v-if="formData.visibility === 'group'">
                  <span class="setting-label">可见用户组</span>
                  <n-select
                    v-model:value="formData.visible_groups"
                    :options="userGroupOptions"
                    multiple
                    filterable
                    placeholder="选择可见用户组"
                    :loading="userGroupsLoading"
                  />
                </div>

                <div class="setting-item">
                  <span class="setting-label">状态</span>
                  <n-select
                    v-model:value="formData.status"
                    :options="statusOptions"
                  />
                </div>

                <div class="action-buttons">
                  <n-button
                    @click="handleSaveDraft"
                    :loading="savingDraft"
                    class="draft-btn"
                  >
                    保存草稿
                  </n-button>
                  <n-button
                    type="primary"
                    @click="handlePublish"
                    :loading="publishing"
                    class="publish-btn"
                  >
                    发布文章
                  </n-button>
                </div>
              </n-space>
            </n-card>

            <!-- 文章属性 -->
            <n-card class="edit-card setting-card" :bordered="false" title="文章属性">
              <n-space vertical :size="16">
                <div class="setting-item">
                  <span class="setting-label">分类</span>
                  <n-select
                    v-model:value="formData.category"
                    :options="categoryOptions"
                    placeholder="请选择文章分类"
                    clearable
                  />
                </div>

                <div class="setting-item">
                  <span class="setting-label">标签</span>
                  <n-select
                    v-model:value="formData.tags"
                    :options="tagOptions"
                    multiple
                    filterable
                    placeholder="请选择标签"
                    :max-tag-count="3"
                  />
                </div>

                <div class="setting-item">
                  <span class="setting-label">摘要</span>
                  <n-input
                    v-model:value="formData.summary"
                    type="textarea"
                    placeholder="请输入文章摘要"
                    :autosize="{ minRows: 3, maxRows: 6 }"
                    :maxlength="500"
                    show-count
                  />
                </div>
              </n-space>
            </n-card>
          </n-space>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import TinyEditor from '@/components/TinyEditor.vue'
import {
  NCard,
  NForm,
  NFormItem,
  NInput,
  NButton,
  NUpload,
  NSelect,
  NDynamicTags,
  NSpace,
  NGrid,
  NGridItem,
  NTag,
  useThemeVars
} from 'naive-ui'
import { articleApi } from '@/api/modules/article'
import { categoryApi } from '@/api/modules/category'
import { tagApi } from '@/api/modules/tag'
import { userApi } from '@/api/modules/user'
import { userGroupApi } from '@/api/modules/userGroup'
import type { UserGroup } from '@/api/modules/userGroup'
import type { Article, Category, Tag, User } from '@/types/api'
import type { InsertContentGenerator } from 'md-editor-v3'
import type { MdEditor as MdEditorType } from 'md-editor-v3'

const route = useRoute()
const router = useRouter()
const message = useMessage()

const isEdit = computed(() => route.params.id !== undefined)
const formRef = ref<any>(null)
const savingDraft = ref(false)
const publishing = ref(false)
const userGroupsLoading = ref(false)

interface ArticleForm {
  title: string;
  content: string;
  summary?: string;
  category?: string | null;
  tags: string[];
  status: 'draft' | 'published' | 'archived';
  visibility: 'public' | 'private' | 'custom' | 'group';
  visible_to: string[];
  visible_groups: string[];
}

const formData = ref<ArticleForm>({
  title: '',
  content: '',
  summary: '',
  category: null,
  tags: [],
  status: 'draft',
  visibility: 'public',
  visible_to: [],
  visible_groups: []
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度应在2-100个字符之间', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' }
  ]
}

// 选项数据
const categoryOptions = ref<Array<{ label: string; value: string }>>([])
const tagOptions = ref<Array<{ label: string; value: string }>>([])
const userOptions = ref<Array<{ label: string; value: string }>>([])
const userGroupOptions = ref<Array<{ label: string; value: string }>>([])

// 可见性选项
const visibilityOptions = [
  { label: '私密', value: 'private' },
  { label: '公开', value: 'public' },
  { label: '指定用户可见', value: 'custom' },
  { label: '指定用户组可见', value: 'group' }
];

const userInfo = ref<any>(null);

// 状态选项
const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '已发布', value: 'published' },
  { label: '已归档', value: 'archived' }
]

// 主题相关
const themeVars = useThemeVars()
const isDarkMode = computed(() => themeVars.value.common?.baseColor === '#000')

// TinyMCE编辑器引用
const editorRef = ref(null)

// 处理编辑器初始化
const handleEditorInit = (editor) => {
  editorRef.value = editor
  console.log('编辑器初始化完成')
  
  // 添加保存快捷键
  editor.addShortcut('meta+s', '保存文章', () => {
    handleSaveDraft()
  })
}

// 自定义标签渲染
const renderTag = ({ option, handleClose }: any) => {
  return h(
    NTag,
    {
      type: 'success',
      closable: true,
      onClose: () => handleClose()
    },
    { default: () => option.label }
  )
}

// 获取用户信息并设置初始值
const initializeData = async () => {
  try {
    console.log('开始初始化数据...');
    
    // 1. 获取用户信息
    console.log('正在获取用户信息...');
    const userRes = await userApi.getProfile();
    console.log('用户信息响应:', userRes);
    
    if (userRes.data?.code === 0 || userRes.data?.code === 200) {
      userInfo.value = userRes.data.data.user;
      console.log('当前用户信息:', userInfo.value);
      formData.value.visibility = 'private';
    }

    // 2. 获取分类和标签
    console.log('正在获取分类和标签...');
    await fetchCategoriesAndTags();

    // 3. 如果是编辑模式，获取文章详情
    if (isEdit.value && route.params.id) {
      console.log('正在获取文章详情, ID:', route.params.id);
      await fetchArticle(route.params.id as string);
    }

    // 4. 根据可见性获取用户列表
    if (formData.value.visibility === 'custom') {
      console.log('正在获取用户列表...');
      await fetchUsers();
    }
    
    console.log('初始化数据完成');
  } catch (error: any) {
    console.error('初始化数据失败:', {
      error,
      response: error.response,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    
    // 根据错误类型显示不同的错误信息
    if (error.response?.status === 401) {
      message.error('请先登录');
      router.push('/auth/login');
    } else if (error.response?.status === 403) {
      message.error('没有权限访问此文章');
      router.push('/articles');
    } else {
      message.error(error.response?.data?.message || '初始化数据失败');
    }
  }
};

// 获取文章详情
const fetchArticle = async (id: string) => {
  try {
    console.log('开始获取文章详情, ID:', id);
    const token = localStorage.getItem('token');
    console.log('请求头Token:', token ? `Bearer ${token}` : '无');
    
    const res = await articleApi.getArticle(id);
    console.log('文章详情响应:', {
      status: res.status,
      statusText: res.statusText,
      data: res.data
    });
    
    if (res.data?.code === 0 || res.data?.code === 200) {
      const { title, content, summary, category, tags, status, visibility, visible_to, visible_groups } = res.data.data;
      console.log('文章数据:', {
        title,
        category: category?._id,
        tags: tags?.map(t => t._id),
        status,
        visibility,
        visible_to: visible_to?.map(u => u._id),
        visible_groups: visible_groups?.map(g => g._id)
      });
      
      formData.value = {
        title: title || '',
        content: content || '',
        summary: summary || '',
        category: category?.id || null,
        tags: tags?.map(tag => tag.id) || [],
        status: status || 'draft',
        visibility: visibility || 'public',
        visible_to: visible_to?.map(user => user.id) || [],
        visible_groups: visible_groups?.map(group => group.id) || []
      };
      console.log('表单数据已更新:', formData.value);
    } else {
      console.error('获取文章详情失败:', res.data);
      message.error(res.data?.message || '获取文章详情失败');
      router.push('/articles');
    }
  } catch (error: any) {
    console.error('获取文章详情出错:', {
      error,
      response: error.response,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    
    if (error.response?.status === 403) {
      message.error('您没有权限查看此文章');
    } else if (error.response?.status === 401) {
      message.error('请先登录后再查看');
      router.push('/auth/login');
    } else {
      message.error(error.response?.data?.message || '获取文章详情失败');
    }
    router.push('/articles');
  }
};

// 获取分类和标签
const fetchCategoriesAndTags = async () => {
  try {
    const [categoriesRes, tagsRes] = await Promise.all([
      categoryApi.getCategories(),
      tagApi.getTags()
    ])
    
    console.log('获取分类响应:', categoriesRes)
    console.log('获取标签响应:', tagsRes)
    
    if (categoriesRes.data?.code === 0 || categoriesRes.data?.code === 200) {
      const categories = Array.isArray(categoriesRes.data.data.items) 
        ? categoriesRes.data.data.items 
        : categoriesRes.data.data
      categoryOptions.value = categories.map(category => ({
        label: category.name,
        value: category.id
      }))
    }
    
    if (tagsRes.data?.code === 0 || tagsRes.data?.code === 200) {
      const tags = Array.isArray(tagsRes.data.data.items) 
        ? tagsRes.data.data.items 
        : tagsRes.data.data
      tagOptions.value = tags.map(tag => ({
        label: tag.name,
        value: tag.id
      }))
    }
  } catch (error: any) {
    console.error('获取分类和标签失败:', error)
    message.error(error.response?.data?.message || '获取分类和标签失败')
  }
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    // 获取用户列表
    const usersRes = await userApi.getUsers();
    console.log('获取用户列表响应:', usersRes);
    
    if (usersRes.data?.code === 0 || usersRes.data?.code === 200) {
      // 处理不同格式的响应数据
      let userList: any[] = [];
      if (Array.isArray(usersRes.data.data)) {
        userList = usersRes.data.data;
      } else if (usersRes.data.data?.items && Array.isArray(usersRes.data.data.items)) {
        userList = usersRes.data.data.items;
      }
      
      userOptions.value = userList
        .filter(user => user._id !== userInfo.value?._id) // 使用已获取的用户信息
        .map(user => ({
          label: user.nickname || user.username,
          value: user._id
        }));
      console.log('处理后的用户选项:', userOptions.value);
    }
  } catch (error: any) {
    console.error('获取用户列表失败:', error);
    message.error(error.response?.data?.message || '获取用户列表失败');
    // 如果获取失败，自动切换为私密可见
    formData.value.visibility = 'private';
  }
};

// 获取用户组列表
const fetchUserGroups = async () => {
  userGroupsLoading.value = true;
  try {
    console.log('开始获取用户组列表...');
    const res = await userGroupApi.getUserGroups();
    console.log('用户组列表响应:', res);
    
    if (res.data?.code === 0 && Array.isArray(res.data.data)) {
      userGroupOptions.value = res.data.data.map(group => ({
        label: group.name,
        value: group._id
      }));
      console.log('处理后的用户组选项:', userGroupOptions.value);
    } else {
      console.error('获取用户组列表失败:', res.data);
      message.error(res.data?.message || '获取用户组列表失败');
    }
  } catch (error: any) {
    console.error('获取用户组列表失败:', {
      error,
      response: error.response,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    message.error('获取用户组列表失败');
  } finally {
    userGroupsLoading.value = false;
  }
};

// 保存草稿
const handleSaveDraft = async () => {
  try {
    savingDraft.value = true
    formData.value.status = 'draft'
    
    const articleData = {
      title: formData.value.title.trim(),
      content: formData.value.content,
      summary: formData.value.summary?.trim(),
      category: formData.value.category,
      tags: formData.value.tags,
      status: formData.value.status,
      visibility: formData.value.visibility,
      visible_to: formData.value.visible_to,
      visible_groups: formData.value.visible_groups
    }
    
    let res;
    if (isEdit.value) {
      res = await articleApi.updateArticle(route.params.id as string, articleData)
    } else {
      res = await articleApi.createArticle(articleData)
    }

    if (res.code === 0 || res.code === 200) {
      message.success('草稿保存成功')
      router.push('/articles')
    } else {
      message.error(res.message || '保存失败')
    }
  } catch (error: any) {
    message.error(error.response?.data?.message || '保存失败')
  } finally {
    savingDraft.value = false
  }
}

// 发布文章
const handlePublish = () => {
  formRef.value?.validate(async (errors: any) => {
    if (errors) {
      return message.error(errors[0]?.[0]?.message || '表单验证失败');
    }

    if (!formData.value.title?.trim()) {
      return message.error('标题不能为空');
    }

    if (!formData.value.content?.trim()) {
      return message.error('内容不能为空');
    }

    try {
      publishing.value = true;
      formData.value.status = 'published';
      
      // 处理数据，移除空值
      const articleData = {
        title: formData.value.title.trim(),
        content: formData.value.content.trim(),
        summary: formData.value.summary?.trim() || undefined,
        category: formData.value.category || null,
        tags: formData.value.tags?.length ? formData.value.tags : [],
        status: formData.value.status,
        visibility: formData.value.visibility,
        visible_to: formData.value.visibility === 'custom' ? formData.value.visible_to : [],
        visible_groups: formData.value.visibility === 'group' ? formData.value.visible_groups : []
      };
      
      // 数据验证
      if (!articleData.category) {
        message.error('请选择文章分类');
        return;
      }

      if (articleData.visibility === 'custom' && (!articleData.visible_to || articleData.visible_to.length === 0)) {
        message.error('请选择可见用户');
        return;
      }

      if (articleData.visibility === 'group' && (!articleData.visible_groups || articleData.visible_groups.length === 0)) {
        message.error('请选择可见用户组');
        return;
      }
      
      console.log('发布文章数据:', {
        ...articleData,
        contentLength: articleData.content.length,
        tagsCount: articleData.tags.length,
        visibleToCount: articleData.visible_to.length,
        visibleGroupsCount: articleData.visible_groups.length
      });
      
      let res;
      if (isEdit.value) {
        res = await articleApi.updateArticle(route.params.id as string, articleData);
      } else {
        res = await articleApi.createArticle(articleData);
      }
      
      console.log('发布文章响应:', res);

      // 检查响应状态 - 修复后的条件检查
      if (res.data?.code === 0 || res.data?.code === 200 || 
          res.data?.message === '文章创建成功' || 
          res.data?.message === '文章更新成功' ||
          res.data?.message === '更新成功' ||
          res.data?.message === '创建成功') {
        message.success(isEdit.value ? '文章更新成功' : '文章发布成功');
        router.push('/articles');
        return;
      }
      
      // 如果没有成功状态，则抛出错误
      throw new Error(res.data?.message || '发布失败');
    } catch (error: any) {
      // 如果错误消息是成功消息，说明实际上是成功的
      if (error.message === '文章创建成功' || 
          error.message === '文章更新成功' || 
          error.message === '更新成功' ||
          error.message === '创建成功') {
        message.success(isEdit.value ? '文章更新成功' : '文章发布成功');
        router.push('/articles');
        return;
      }

      console.error('发布文章详细错误:', {
        error,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data,
        message: error.message || error.response?.data?.message
      });
      
      if (error.response?.status === 401) {
        message.error('登录已过期，请重新登录');
        router.push('/login');
      } else {
        message.error(error.response?.data?.message || error.message || '发布失败');
      }
    } finally {
      publishing.value = false;
    }
  });
};

// 处理可见性变更
const handleVisibilityChange = async (value: string) => {
  if (value === 'custom' && userOptions.value.length === 0) {
    try {
      await fetchUsers();
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败，已切换为私密可见');
      formData.value.visibility = 'private';
    }
  } else if (value === 'group' && userGroupOptions.value.length === 0) {
    try {
      await fetchUserGroups();
    } catch (error) {
      console.error('获取用户组列表失败:', error);
      message.error('获取用户组列表失败，已切换为私密可见');
      formData.value.visibility = 'private';
    }
  }
  
  if (value !== 'custom') {
    formData.value.visible_to = [];
  }
  if (value !== 'group') {
    formData.value.visible_groups = [];
  }
};

onMounted(() => {
  initializeData();
  if (formData.value.visibility === 'group') {
    fetchUserGroups();
  }
});
</script>

<style lang="scss" scoped>
.article-edit {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem 0;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0;
      background: linear-gradient(120deg, #2c3e50, #3498db);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .subtitle {
      color: #666;
      margin-top: 0.5rem;
      font-size: 1.1rem;
    }
  }

  .content-wrapper {
    max-width: 2000px;
    margin: 0 auto;
    padding: 0 2rem;

    .edit-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: transform 0.3s ease;

      &.main-card {
        min-height: 800px;
      }

      &.setting-card {
        margin-bottom: 1.5rem;

        .n-card-header {
          font-size: 1.1rem;
          font-weight: 600;
          color: #2c3e50;
          border-bottom: 1px solid rgba(0, 0, 0, 0.06);
          padding-bottom: 1rem;
        }
      }

      .setting-item {
        .setting-label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 500;
          color: #2c3e50;
        }
      }

      .upload-btn {
        min-width: 100px;
        margin-bottom: 8px;
        
        .btn-icon {
          font-size: 16px;
        }
      }
      
      .file-upload-btn {
        width: 100%;
        font-size: 14px;
        padding: 8px 12px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .action-buttons {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(0, 0, 0, 0.06);

        .draft-btn, .publish-btn {
          flex: 1;
        }
      }

      .editor-wrapper {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        overflow: hidden;
        min-height: 600px;
        width: 100%;
        display: flex;
        flex-direction: column;

        /* 确保编辑器撑满容器 */
        :deep(.tiny-editor-wrapper) {
          width: 100%;
          min-height: 600px;
          flex: 1;
          display: flex;
          flex-direction: column;
        }

        /* TinyMCE样式调整 */
        :deep(.tox-tinymce) {
          flex: 1;
          display: flex;
          flex-direction: column;
          border: none !important; /* 避免双重边框 */
        }

        :deep(.tox-editor-container) {
          display: flex;
          flex-direction: column;
          flex: 1;
        }

        :deep(.tox-sidebar-wrap) {
          flex: 1;
        }

        :deep(.tox-edit-area) {
          flex: 1;
        }

        /* 隐藏不必要的元素 */
        :deep(.tox-toolbar-overlord) {
          position: sticky;
          top: 0;
          z-index: 1;
          background: white;
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .article-edit {
    .content-wrapper {
      padding: 0 1rem;
      max-width: 100%;

      :deep(.n-grid) {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        .n-grid-item {
          width: 100% !important;
        }
      }
      
      .editor-wrapper {
        :deep(.md-editor-toolbar) {
          padding: 8px;
          
          .md-editor-toolbar-item {
            padding: 5px;
            margin: 4px;
            
            button {
              width: 36px;
              height: 36px;
            }
            
            svg {
              width: 24px;
              height: 24px;
            }
          }
        }
      }
      
      .file-upload-btn {
        padding: 6px 10px;
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 768px) {
  .article-edit {
    .content-wrapper {
      .editor-wrapper {
        :deep(.md-editor-toolbar) {
          padding: 6px;
          
          .md-editor-toolbar-item {
            padding: 4px;
            margin: 3px;
            
            button {
              width: 32px;
              height: 32px;
            }
            
            svg {
              width: 22px;
              height: 22px;
            }
          }
        }
      }
      
      .file-upload-btn {
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }
}
</style> 