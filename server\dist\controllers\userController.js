"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePassword = exports.login = exports.register = exports.deleteUser = exports.updateUser = exports.createUser = exports.getUser = exports.getUsers = void 0;
const express_validator_1 = require("express-validator");
const sequelize_1 = require("sequelize");
const User_1 = __importDefault(require("../models/User"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const catchAsync_1 = require("../utils/catchAsync");
const appError_1 = __importDefault(require("../utils/appError"));
// 生成JWT token
const generateToken = (id) => {
    return jsonwebtoken_1.default.sign({ id }, process.env.JWT_SECRET || 'your-secret-key', {
        expiresIn: '7d'
    });
};
// @desc    获取所有用户
// @route   GET /api/users
// @access  Private
exports.getUsers = (0, catchAsync_1.catchAsync)(async (req, res) => {
    const isAdmin = req.query.isAdmin === 'true';
    const isAdminUser = req.user?.role === 'admin';
    const search = req.query.search;
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const skip = (page - 1) * pageSize;
    // 构建查询条件
    const whereClause = {};
    if (search) {
        whereClause[sequelize_1.Op.or] = [
            { username: { [sequelize_1.Op.like]: `%${search}%` } },
            { email: { [sequelize_1.Op.like]: `%${search}%` } },
            { nickname: { [sequelize_1.Op.like]: `%${search}%` } }
        ];
    }
    // 如果是后台请求但不是管理员
    if (isAdmin && !isAdminUser) {
        return res.status(403).json({
            code: 403,
            message: '没有权限访问'
        });
    }
    // 后台管理请求 - 返回完整用户信息
    if (isAdmin && isAdminUser) {
        const total = await User_1.default.count({ where: whereClause });
        const users = await User_1.default.findAll({
            where: whereClause,
            attributes: { exclude: ['password'] },
            order: [['created_at', 'DESC']],
            offset: skip,
            limit: pageSize
        });
        return res.status(200).json({
            code: 200,
            message: '获取用户列表成功',
            data: {
                total,
                items: users,
                page,
                pageSize
            }
        });
    }
    // 前台请求 - 只返回基本信息
    const total = await User_1.default.count({ where: whereClause });
    const users = await User_1.default.findAll({
        where: whereClause,
        attributes: ['username', 'nickname', 'email', 'avatar', 'bio'],
        order: [['created_at', 'DESC']],
        offset: skip,
        limit: pageSize
    });
    res.status(200).json({
        code: 200,
        message: '获取用户列表成功',
        data: {
            total,
            items: users,
            page,
            pageSize
        }
    });
});
// @desc    获取单个用户
// @route   GET /api/users/:id
// @access  Private/Admin
exports.getUser = (0, catchAsync_1.catchAsync)(async (req, res) => {
    const user = await User_1.default.findByPk(req.params.id, {
        attributes: { exclude: ['password'] }
    });
    if (!user) {
        throw new appError_1.default('未找到该用户', 404);
    }
    res.status(200).json({
        code: 200,
        message: '获取用户信息成功',
        data: user
    });
});
// @desc    创建用户
// @route   POST /api/users
// @access  Private/Admin
exports.createUser = (0, catchAsync_1.catchAsync)(async (req, res) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new appError_1.default('验证错误', 400);
    }
    const { username, email, password, role, nickname, bio } = req.body;
    // 检查用户名是否已存在
    const existingUsername = await User_1.default.findOne({ where: { username } });
    if (existingUsername) {
        throw new appError_1.default('用户名已存在', 400);
    }
    // 检查邮箱是否已存在
    const existingEmail = await User_1.default.findOne({ where: { email } });
    if (existingEmail) {
        throw new appError_1.default('邮箱已存在', 400);
    }
    // 创建用户
    const user = await User_1.default.create({
        username,
        email,
        password,
        role,
        nickname,
        bio,
        avatar: '/uploads/touxiang.png',
        is_active: true
    });
    res.status(201).json({
        code: 201,
        message: '用户创建成功',
        data: user
    });
});
// @desc    更新用户
// @route   PUT /api/users/:id
// @access  Private/Admin
exports.updateUser = (0, catchAsync_1.catchAsync)(async (req, res) => {
    const updates = {
        ...req.body
    };
    // 如果更新包含密码，需要删除它，密码更新应该使用专门的接口
    delete updates.password;
    const [affectedCount] = await User_1.default.update(updates, {
        where: { id: req.params.id }
    });
    if (affectedCount === 0) {
        throw new appError_1.default('未找到该用户', 404);
    }
    const user = await User_1.default.findByPk(req.params.id, {
        attributes: { exclude: ['password'] }
    });
    res.status(200).json({
        code: 200,
        message: '更新用户信息成功',
        data: user
    });
});
// @desc    删除用户
// @route   DELETE /api/users/:id
// @access  Private/Admin
exports.deleteUser = (0, catchAsync_1.catchAsync)(async (req, res) => {
    const user = await User_1.default.findByPk(req.params.id);
    if (!user) {
        throw new appError_1.default('未找到该用户', 404);
    }
    // 不允许删除自己
    if (req.user && req.user.id === parseInt(req.params.id)) {
        throw new appError_1.default('不能删除当前登录的用户', 400);
    }
    await user.destroy();
    res.status(200).json({
        code: 200,
        message: '删除用户成功'
    });
});
// 注册
exports.register = (0, catchAsync_1.catchAsync)(async (req, res) => {
    const { username, email, password, nickname, bio } = req.body;
    // 检查用户是否已存在
    const existingUser = await User_1.default.findOne({
        where: {
            [sequelize_1.Op.or]: [{ email }, { username }]
        }
    });
    if (existingUser) {
        throw new appError_1.default('用户名或邮箱已被使用', 400);
    }
    // 创建用户
    const user = await User_1.default.create({
        username,
        email,
        password,
        role: 'user',
        nickname,
        bio,
        avatar: '/uploads/touxiang.png',
        is_active: true
    });
    // 生成token
    const token = generateToken(user.id.toString());
    res.status(201).json({
        code: 200,
        message: '注册成功',
        data: {
            token,
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role,
                nickname: user.nickname,
                bio: user.bio
            }
        }
    });
});
// 登录
exports.login = (0, catchAsync_1.catchAsync)(async (req, res) => {
    const { username, password } = req.body;
    // 检查用户名和密码是否提供
    if (!username || !password) {
        throw new appError_1.default('请提供用户名和密码', 400);
    }
    // 查找用户
    const user = await User_1.default.findOne({
        where: {
            [sequelize_1.Op.or]: [{ username }, { email: username }],
            is_active: true
        }
    });
    if (!user || !(await user.comparePassword(password))) {
        throw new appError_1.default('用户名或密码错误', 401);
    }
    // 更新最后登录时间
    user.last_login = new Date();
    await user.save();
    // 生成token
    const token = generateToken(user.id.toString());
    res.status(200).json({
        code: 200,
        message: '登录成功',
        data: {
            token,
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role,
                nickname: user.nickname,
                bio: user.bio
            }
        }
    });
});
// 修改密码
exports.updatePassword = (0, catchAsync_1.catchAsync)(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.params.id;
    // 参数验证
    if (!newPassword || newPassword.length < 6) {
        return res.status(400).json({
            code: 400,
            message: '新密码长度不能小于6个字符'
        });
    }
    const user = await User_1.default.findByPk(userId);
    if (!user) {
        return res.status(404).json({
            code: 404,
            message: '未找到该用户'
        });
    }
    // 如果是管理员重置密码，不需要验证当前密码
    const isAdmin = req.user && req.user.role === 'admin';
    if (!isAdmin && currentPassword) {
        const isMatch = await user.comparePassword(currentPassword);
        if (!isMatch) {
            return res.status(401).json({
                code: 401,
                message: '当前密码错误'
            });
        }
    }
    // 更新密码
    user.password = newPassword;
    await user.save();
    res.status(200).json({
        code: 200,
        message: '密码修改成功'
    });
});
