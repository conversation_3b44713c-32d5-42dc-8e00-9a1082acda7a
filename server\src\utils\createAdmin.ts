import 'reflect-metadata';
import dotenv from 'dotenv';
import path from 'path';
import { sequelize } from '../config/db';
import { setupAssociations, User } from '../models';

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

async function createAdminUser() {
  try {
    console.log('正在连接到MySQL数据库...');

    // 连接到MySQL
    await sequelize.authenticate();
    setupAssociations();
    await sequelize.sync({ alter: true });
    console.log('MySQL连接成功');

    // 检查是否已存在管理员账号
    const existingAdmin = await User.findOne({ where: { role: 'admin' } });
    if (existingAdmin) {
      console.log('管理员账号已存在:', existingAdmin.username);
      await sequelize.close();
      return;
    }

    // 创建默认管理员账号
    const newAdmin = await User.create({
      username: 'admin',
      password: 'admin123', // 会自动加密
      email: '<EMAIL>',
      role: 'admin',
      nickname: '系统管理员',
      avatar: '',
      is_active: true
    });

    console.log('默认管理员账号创建成功:');
    console.log('用户名: admin');
    console.log('密码: admin123');
    console.log('邮箱: <EMAIL>');
    console.log('请登录后立即修改默认密码!');

    // 断开数据库连接
    await sequelize.close();
    console.log('MySQL连接已关闭');
  } catch (error) {
    console.error('创建管理员账号失败:', error);
    process.exit(1);
  }
}

// 执行创建管理员账号的函数
createAdminUser(); 