import 'reflect-metadata';
import dotenv from 'dotenv';
// import mongoose from 'mongoose';
import { sequelize } from '../config/db';
import { setupAssociations, User, Category, Tag, UserGroup, Article, Setting, Announcement } from '../models';

dotenv.config();

interface MongoUser {
  _id: string;
  username: string;
  email: string;
  password: string;
  role: 'user' | 'admin';
  nickname?: string;
  bio?: string;
  avatar?: string;
  is_active: boolean;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

interface MongoCategory {
  _id: string;
  name: string;
  description?: string;
  slug: string;
  created_at: Date;
  updated_at: Date;
}

interface MongoTag {
  _id: string;
  name: string;
  description?: string;
  slug: string;
  created_at: Date;
  updated_at: Date;
}

interface MongoUserGroup {
  _id: string;
  name: string;
  description?: string;
  members: string[];
  created_at: Date;
  updated_at: Date;
}

interface MongoArticle {
  _id: string;
  title: string;
  content: string;
  summary?: string;
  slug: string;
  cover_image?: string;
  status: 'draft' | 'published' | 'archived';
  visibility: 'public' | 'private' | 'custom' | 'group';
  visible_to: string[];
  visible_groups: string[];
  view_count: number;
  author: string;
  category?: string;
  tags: string[];
  created_at: Date;
  updated_at: Date;
  published_at?: Date;
}

async function migrateData() {
  try {
    console.log('开始数据迁移...');
    
    // 连接MongoDB
    if (process.env.MONGODB_URI) {
      await mongoose.connect(process.env.MONGODB_URI);
      console.log('MongoDB连接成功');
    } else {
      console.log('未配置MONGODB_URI，跳过数据迁移');
      return;
    }
    
    // 连接MySQL
    await sequelize.authenticate();
    setupAssociations();
    await sequelize.sync({ force: true }); // 清空并重建表
    console.log('MySQL连接成功，表结构已重建');
    
    // 创建ID映射
    const userIdMap = new Map<string, number>();
    const categoryIdMap = new Map<string, number>();
    const tagIdMap = new Map<string, number>();
    const userGroupIdMap = new Map<string, number>();
    
    // 迁移用户数据
    console.log('迁移用户数据...');
    const mongoUsers = await mongoose.connection.db.collection('users').find({}).toArray() as MongoUser[];
    for (const mongoUser of mongoUsers) {
      const user = await User.create({
        username: mongoUser.username,
        email: mongoUser.email,
        password: mongoUser.password, // 密码已经加密，直接使用
        role: mongoUser.role,
        nickname: mongoUser.nickname,
        bio: mongoUser.bio,
        avatar: mongoUser.avatar || '/uploads/touxiang.png',
        is_active: mongoUser.is_active,
        last_login: mongoUser.last_login,
        created_at: mongoUser.created_at,
        updated_at: mongoUser.updated_at,
      });
      userIdMap.set(mongoUser._id, user.id);
    }
    console.log(`用户数据迁移完成: ${mongoUsers.length} 条记录`);
    
    // 迁移分类数据
    console.log('迁移分类数据...');
    const mongoCategories = await mongoose.connection.db.collection('categories').find({}).toArray() as MongoCategory[];
    for (const mongoCategory of mongoCategories) {
      const category = await Category.create({
        name: mongoCategory.name,
        description: mongoCategory.description,
        slug: mongoCategory.slug,
        created_at: mongoCategory.created_at,
        updated_at: mongoCategory.updated_at,
      });
      categoryIdMap.set(mongoCategory._id, category.id);
    }
    console.log(`分类数据迁移完成: ${mongoCategories.length} 条记录`);
    
    // 迁移标签数据
    console.log('迁移标签数据...');
    const mongoTags = await mongoose.connection.db.collection('tags').find({}).toArray() as MongoTag[];
    for (const mongoTag of mongoTags) {
      const tag = await Tag.create({
        name: mongoTag.name,
        description: mongoTag.description,
        slug: mongoTag.slug,
        created_at: mongoTag.created_at,
        updated_at: mongoTag.updated_at,
      });
      tagIdMap.set(mongoTag._id, tag.id);
    }
    console.log(`标签数据迁移完成: ${mongoTags.length} 条记录`);
    
    console.log('数据迁移完成！');
    
  } catch (error) {
    console.error('数据迁移失败:', error);
  } finally {
    await mongoose.disconnect();
    await sequelize.close();
    process.exit(0);
  }
}

migrateData();
