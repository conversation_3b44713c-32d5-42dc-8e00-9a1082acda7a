"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleVisibleGroup = exports.ArticleVisibleUser = exports.ArticleTag = exports.UserGroupMember = void 0;
const sequelize_1 = require("sequelize");
const db_1 = require("../config/db");
// 用户-用户组关联表
class UserGroupMember extends sequelize_1.Model {
}
exports.UserGroupMember = UserGroupMember;
UserGroupMember.init({
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        references: {
            model: 'users',
            key: 'id'
        }
    },
    user_group_id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        references: {
            model: 'user_groups',
            key: 'id'
        }
    }
}, {
    sequelize: db_1.sequelize,
    modelName: 'UserGroupMember',
    tableName: 'user_group_members',
    timestamps: false
});
// 文章-标签关联表
class ArticleTag extends sequelize_1.Model {
}
exports.ArticleTag = ArticleTag;
ArticleTag.init({
    article_id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        references: {
            model: 'articles',
            key: 'id'
        }
    },
    tag_id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        references: {
            model: 'tags',
            key: 'id'
        }
    }
}, {
    sequelize: db_1.sequelize,
    modelName: 'ArticleTag',
    tableName: 'article_tags',
    timestamps: false
});
// 文章-可见用户关联表
class ArticleVisibleUser extends sequelize_1.Model {
}
exports.ArticleVisibleUser = ArticleVisibleUser;
ArticleVisibleUser.init({
    article_id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        references: {
            model: 'articles',
            key: 'id'
        }
    },
    user_id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        references: {
            model: 'users',
            key: 'id'
        }
    }
}, {
    sequelize: db_1.sequelize,
    modelName: 'ArticleVisibleUser',
    tableName: 'article_visible_users',
    timestamps: false
});
// 文章-可见用户组关联表
class ArticleVisibleGroup extends sequelize_1.Model {
}
exports.ArticleVisibleGroup = ArticleVisibleGroup;
ArticleVisibleGroup.init({
    article_id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        references: {
            model: 'articles',
            key: 'id'
        }
    },
    user_group_id: {
        type: sequelize_1.DataTypes.INTEGER,
        primaryKey: true,
        references: {
            model: 'user_groups',
            key: 'id'
        }
    }
}, {
    sequelize: db_1.sequelize,
    modelName: 'ArticleVisibleGroup',
    tableName: 'article_visible_groups',
    timestamps: false
});
