"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sequelize = void 0;
const sequelize_1 = require("sequelize");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
// 创建Sequelize实例
exports.sequelize = new sequelize_1.Sequelize({
    dialect: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'admin_system',
    // 连接池配置
    pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000
    },
    // 日志配置
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    // 时区配置
    timezone: '+08:00',
    // 其他配置
    define: {
        timestamps: true,
        underscored: false,
        freezeTableName: true,
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci'
    }
});
// 数据库连接函数
const connectDB = async () => {
    try {
        await exports.sequelize.authenticate();
        console.log(`MySQL连接成功: ${process.env.DB_HOST || 'localhost'}:${process.env.DB_PORT || '3306'}`);
        // 导入并设置模型关联关系
        const { setupAssociations } = await Promise.resolve().then(() => __importStar(require('../models')));
        setupAssociations();
        console.log('模型关联关系设置完成');
        // 在开发环境下同步数据库结构
        if (process.env.NODE_ENV === 'development') {
            await exports.sequelize.sync({ alter: true });
            console.log('数据库表结构同步完成');
        }
    }
    catch (error) {
        console.error(`MySQL连接失败: ${error.message}`);
        process.exit(1);
    }
};
exports.default = connectDB;
