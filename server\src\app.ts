import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';

import compression from 'compression';
import AppError from './utils/appError';
import { errorHandler } from './middleware/error';
import settingRoutes from './routes/settingRoutes';
import announcementRoutes from './routes/announcementRoutes';
import authRoutes from './routes/authRoutes';
import userRoutes from './routes/userRoutes';
import userGroupRoutes from './routes/userGroupRoutes';
import dashboardRoutes from './routes/dashboardRoutes';
import articleRoutes from './routes/articleRoutes';
import categoryRoutes from './routes/categoryRoutes';
import tagRoutes from './routes/tagRoutes';
import uploadRoutes from './routes/uploadRoutes';
import path from 'path';
import cookieParser from 'cookie-parser';

const app = express();

// 安全相关中间件
app.use(helmet({
  contentSecurityPolicy: false // 禁用 CSP 以允许前端资源加载
}));

// CORS 配置
app.use(cors({
  origin: true, // 允许所有来源
  credentials: true, // 允许跨域请求携带凭据
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// MongoDB sanitize 已移除，使用MySQL

// 解析请求
app.use(express.json({ limit: '500mb' }));
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser()); // 解析 Cookie

// 日志
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
  console.log('Running in development mode');
} else {
  console.log('Running in production mode');
}

// 压缩响应
app.use(compression());

// 创建 API 路由器
const apiRouter = express.Router();

// API 路由注册
apiRouter.use('/auth', authRoutes);
apiRouter.use('/users', userRoutes);
apiRouter.use('/user-groups', userGroupRoutes);
apiRouter.use('/dashboard', dashboardRoutes);
apiRouter.use('/articles', articleRoutes);
apiRouter.use('/categories', categoryRoutes);
apiRouter.use('/tags', tagRoutes);
apiRouter.use('/announcements', announcementRoutes);
apiRouter.use('/settings', settingRoutes);
apiRouter.use('/upload', uploadRoutes);

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// 将 API 路由器挂载到主应用
app.use('/api', apiRouter);

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// 前端静态文件服务 - 博客前台
app.use('/', express.static(path.join(__dirname, '../public/blog')));

// 前端静态文件服务 - 管理后台
app.use('/admin', express.static(path.join(__dirname, '../public/admin')));

// 设置信任代理，解决 express-rate-limit 的警告
app.set('trust proxy', 1);

// 请求限制
const limiter = rateLimit({
  max: 1000,
  windowMs: 5 * 60 * 1000,
  message: '请求过于频繁，请稍后再试',
  // 添加以下配置来解决X-Forwarded-For警告
  standardHeaders: true,
  legacyHeaders: false
});
app.use(limiter);

// 前端路由处理 - 博客前台
app.get('*', (req, res, next) => {
  // 如果是API请求或管理后台请求，跳过该中间件
  if (req.path.startsWith('/api/') || req.path.startsWith('/admin/')) {
    return next();
  }
  
  // 服务博客前台的HTML文件
  res.sendFile(path.join(__dirname, '../public/blog/index.html'));
});

// 前端路由处理 - 管理后台
app.get('/admin/*', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/admin/index.html'));
});

// 处理未匹配的路由
app.all('*', (req, res, next) => {
  console.log(`404 - Not Found: ${req.method} ${req.originalUrl}`);
  next(new AppError(`找不到路径: ${req.originalUrl}`, 404));
});

// 错误处理
app.use(errorHandler);

export default app; 