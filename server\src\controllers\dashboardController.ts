import { Request, Response, NextFunction } from 'express';
import { Op } from 'sequelize';
import Article from '../models/Article';
import User from '../models/User';
import Category from '../models/Category';
import Tag from '../models/Tag';

interface ArticleStats {
  _id: 'draft' | 'published' | 'archived';
  count: number;
}

interface FormattedArticleStats {
  total: number;
  published: number;
  draft: number;
  archived: number;
  [key: string]: number;
}

// @desc    获取仪表盘统计数据
// @route   GET /api/dashboard/stats
// @access  Private
export const getStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // 获取文章统计
    const totalArticles = await Article.count();
    const publishedArticles = await Article.count({ where: { status: 'published' } });
    const draftArticles = await Article.count({ where: { status: 'draft' } });
    const archivedArticles = await Article.count({ where: { status: 'archived' } });

    // 获取用户统计
    const userCount = await User.count();
    const activeUserCount = await User.count({ where: { is_active: true } });

    // 获取分类统计
    const categoryCount = await Category.count();

    // 获取标签统计
    const tagCount = await Tag.count();

    // 获取过去7天的文章发布趋势
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const articleTrend = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(sevenDaysAgo);
      date.setDate(date.getDate() + i);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const count = await Article.count({
        where: {
          created_at: {
            [Op.gte]: startOfDay,
            [Op.lte]: endOfDay
          },
          status: 'published'
        }
      });

      articleTrend.push({
        date: startOfDay.toISOString().split('T')[0],
        count
      });
    }

    // 获取最近的文章
    const recentArticles = await Article.findAll({
      include: [
        { model: User, as: 'author', attributes: ['username'] },
        { model: Category, as: 'category', attributes: ['name'] }
      ],
      order: [['created_at', 'DESC']],
      limit: 5,
      attributes: ['id', 'title', 'status', 'view_count', 'created_at']
    });

    // 获取热门文章
    const popularArticles = await Article.findAll({
      include: [
        { model: User, as: 'author', attributes: ['username'] },
        { model: Category, as: 'category', attributes: ['name'] }
      ],
      order: [['view_count', 'DESC']],
      limit: 5,
      attributes: ['id', 'title', 'status', 'view_count', 'created_at']
    });

    // 格式化文章统计数据
    const formattedArticleStats = {
      total: totalArticles,
      published: publishedArticles,
      draft: draftArticles,
      archived: archivedArticles
    };

    res.status(200).json({
      code: 200,
      message: '获取仪表盘数据成功',
      data: {
        articles: formattedArticleStats,
        articleTrend,
        users: {
          total: userCount,
          active: activeUserCount
        },
        categories: {
          total: categoryCount
        },
        tags: {
          total: tagCount
        },
        recentArticles,
        popularArticles
      }
    });
  } catch (error) {
    next(error);
  }
}; 