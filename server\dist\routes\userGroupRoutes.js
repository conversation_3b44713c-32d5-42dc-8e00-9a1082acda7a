"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const userGroupController = __importStar(require("../controllers/userGroupController"));
const authMiddleware_1 = require("../middleware/authMiddleware");
const router = express_1.default.Router();
// 保护所有路由，只有登录用户才能访问
router.use(authMiddleware_1.protect);
// 所有登录用户都可以访问的路由
router.get('/', userGroupController.getAllUserGroups);
router.get('/:id', userGroupController.getUserGroup);
// 管理员路由
const adminRouter = express_1.default.Router();
adminRouter.use((0, authMiddleware_1.restrictTo)('admin'));
// 管理员操作路由
adminRouter.post('/', userGroupController.createUserGroup);
adminRouter.patch('/:id', userGroupController.updateUserGroup);
adminRouter.delete('/:id', userGroupController.deleteUserGroup);
adminRouter.patch('/:groupId/members/:userId', userGroupController.addMemberToGroup);
adminRouter.delete('/:groupId/members/:userId', userGroupController.removeMemberFromGroup);
// 获取用户所属的用户组
router.get('/user/:userId', userGroupController.getUserGroups);
// 将管理员路由添加到主路由
router.use('/', adminRouter);
exports.default = router;
