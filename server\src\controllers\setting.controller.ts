import { Request, Response } from 'express';
import Setting from '../models/Setting';
import { catchAsync } from '../utils/catchAsync';
import AppError from '../utils/appError';

// 获取所有设置
export const getSettings = catchAsync(async (req: Request, res: Response) => {
  const { group } = req.query;

  const whereClause = group ? { group: group as string } : {};
  const settings = await Setting.findAll({
    where: whereClause,
    order: [['key', 'ASC']]
  });

  // 将设置按组分类
  const groupedSettings = settings.reduce((acc: any, setting) => {
    if (!acc[setting.group]) {
      acc[setting.group] = {};
    }
    acc[setting.group][setting.key] = setting.value;
    return acc;
  }, {});

  res.status(200).json({
    status: 'success',
    data: {
      settings: groupedSettings
    }
  });
});

// 更新设置
export const updateSettings = catchAsync(async (req: Request, res: Response) => {
  const { group, settings } = req.body;

  if (!group || !settings || typeof settings !== 'object') {
    throw new AppError('请提供有效的设置组和设置数据', 400);
  }

  // 批量更新设置
  for (const [key, value] of Object.entries(settings)) {
    await Setting.upsert({
      key,
      group,
      value: value as string
    });
  }

  res.status(200).json({
    status: 'success',
    message: '设置更新成功'
  });
});

// 初始化默认设置
export const initializeSettings = catchAsync(async (req: Request, res: Response) => {
  const defaultSettings = [
    // 基本设置
    {
      key: 'siteName',
      value: '我的博客',
      group: 'basic',
      description: '网站名称'
    },
    {
      key: 'siteDescription',
      value: '一个简单的博客系统',
      group: 'basic',
      description: '网站描述'
    },
    {
      key: 'siteKeywords',
      value: '博客,技术,生活',
      group: 'basic',
      description: '网站关键词'
    },
    {
      key: 'siteFooter',
      value: '© 2024 我的博客',
      group: 'basic',
      description: '网站页脚'
    },

    // 安全设置
    {
      key: 'enableRegistration',
      value: true,
      group: 'security',
      description: '是否允许注册'
    },
    {
      key: 'requireEmailVerification',
      value: true,
      group: 'security',
      description: '是否需要邮箱验证'
    },
    {
      key: 'maxLoginAttempts',
      value: 5,
      group: 'security',
      description: '最大登录尝试次数'
    },
    {
      key: 'lockoutDuration',
      value: 30,
      group: 'security',
      description: '账户锁定时长（分钟）'
    },

    // 日志设置
    {
      key: 'enableAccessLog',
      value: true,
      group: 'log',
      description: '是否启用访问日志'
    },
    {
      key: 'logRetentionDays',
      value: 30,
      group: 'log',
      description: '日志保留天数'
    },
    {
      key: 'enableErrorLog',
      value: true,
      group: 'log',
      description: '是否启用错误日志'
    },

    // 站点设置
    {
      key: 'theme',
      value: 'light',
      group: 'site',
      description: '网站主题'
    },
    {
      key: 'articlesPerPage',
      value: 10,
      group: 'site',
      description: '每页文章数'
    },
    {
      key: 'enableComments',
      value: true,
      group: 'site',
      description: '是否启用评论'
    },
    {
      key: 'commentNeedsApproval',
      value: true,
      group: 'site',
      description: '评论是否需要审核'
    },

    // 公告设置
    {
      key: 'announcement',
      value: '',
      group: 'announcement',
      description: '网站公告'
    },
    {
      key: 'enableAnnouncement',
      value: false,
      group: 'announcement',
      description: '是否显示公告'
    },
    {
      key: 'announcementStyle',
      value: 'info',
      group: 'announcement',
      description: '公告样式'
    }
  ];

  // 批量插入默认设置
  for (const setting of defaultSettings) {
    try {
      await Setting.create({
        key: setting.key,
        value: setting.value.toString(),
        group: setting.group,
        description: setting.description
      });
    } catch (error) {
      // 忽略重复键错误
      console.log(`设置 ${setting.key} 已存在，跳过`);
    }
  }

  res.status(200).json({
    status: 'success',
    message: '默认设置初始化成功'
  });
}); 