"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserGroup = void 0;
const sequelize_1 = require("sequelize");
const db_1 = require("../config/db");
class UserGroup extends sequelize_1.Model {
}
exports.UserGroup = UserGroup;
UserGroup.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    },
    name: {
        type: sequelize_1.DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        validate: {
            notEmpty: { msg: '用户组名称是必需的' },
            len: { args: [1, 50], msg: '用户组名称不能超过50个字符' }
        }
    },
    description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
        validate: {
            len: { args: [0, 500], msg: '用户组描述不能超过500个字符' }
        }
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        defaultValue: sequelize_1.DataTypes.NOW
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        defaultValue: sequelize_1.DataTypes.NOW
    }
}, {
    sequelize: db_1.sequelize,
    modelName: 'UserGroup',
    tableName: 'user_groups',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
});
exports.default = UserGroup;
