import request from '../request'

export interface DashboardStatsResponse {
  code: number
  message: string
  data: {
    articles: {
      total: number
      published: number
      draft: number
      archived: number
    }
    articleTrend: {
      date: string
      count: number
    }[]
    users: {
      total: number
      active: number
    }
    categories: {
      total: number
    }
    tags: {
      total: number
    }
    recentArticles: {
      id: number
      title: string
      status: string
      view_count: number
      created_at: string
      author: {
        id: number
        username: string
      } | null
      category: {
        id: number
        name: string
      } | null
    }[]
    popularArticles: {
      id: number
      title: string
      status: string
      view_count: number
      created_at: string
      author: {
        id: number
        username: string
      } | null
      category: {
        id: number
        name: string
      } | null
    }[]
  }
}

export default {
  // 获取仪表盘统计数据
  getStats: () => {
    return request.get<any, DashboardStatsResponse>('/dashboard/stats')
  }
}
