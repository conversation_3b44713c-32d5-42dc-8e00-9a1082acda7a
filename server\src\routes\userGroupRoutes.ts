import express from 'express';
import * as userGroupController from '../controllers/userGroupController';
import { protect, restrictTo } from '../middleware/authMiddleware';

const router = express.Router();

// 保护所有路由，只有登录用户才能访问
router.use(protect);

// 所有登录用户都可以访问的路由
router.get('/', userGroupController.getAllUserGroups);
router.get('/:id', userGroupController.getUserGroup);

// 管理员路由
const adminRouter = express.Router();
adminRouter.use(restrictTo('admin'));

// 管理员操作路由
adminRouter.post('/', userGroupController.createUserGroup);
adminRouter.patch('/:id', userGroupController.updateUserGroup);
adminRouter.delete('/:id', userGroupController.deleteUserGroup);
adminRouter.patch('/:groupId/members/:userId', userGroupController.addMemberToGroup);
adminRouter.delete('/:groupId/members/:userId', userGroupController.removeMemberFromGroup);

// 获取用户所属的用户组
router.get('/user/:userId', userGroupController.getUserGroups);

// 将管理员路由添加到主路由
router.use('/', adminRouter);

export default router; 