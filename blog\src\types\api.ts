// 通用响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 用户相关
export interface User {
  id: number;
  username: string;
  nickname?: string;
  email: string;
  avatar?: string;
  bio?: string;
  role: string;
  is_active?: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface UserResponse {
  code: number;
  message: string;
  data: {
    user: User;
  };
}

export interface UserListResponse {
  code: number;
  message: string;
  data: {
    items: User[];
    total: number;
  };
}

export interface AuthResponse {
  code: number;
  message: string;
  data: {
    token: string;
    user: User;
  };
}

// 文章相关
export interface Article {
  id: number;
  title: string;
  content: string;
  summary?: string;
  cover_image?: string;
  status: 'published' | 'draft' | 'archived';
  visibility: 'public' | 'private' | 'protected' | 'custom' | 'group';
  visible_to?: number[];
  visible_groups?: number[];
  author: User;
  category?: Category;
  tags?: Tag[];
  view_count: number;
  created_at: string;
  updated_at: string;
}

export interface ArticleResponse {
  code: number;
  message: string;
  data: Article;
}

export interface ArticleListResponse {
  code: number;
  message: string;
  data: {
    items: Article[];
    total: number;
  };
}

// 分类相关
export interface Category {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface CategoryResponse {
  code: number;
  message: string;
  data: {
    category: Category;
  };
}

export interface CategoryListResponse {
  code: number;
  message: string;
  data: {
    items: Category[];
    total: number;
  };
}

// 标签相关
export interface Tag {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface TagResponse {
  code: number;
  message: string;
  data: {
    tag: Tag;
  };
}

export interface TagListResponse {
  code: number;
  message: string;
  data: {
    items: Tag[];
    total: number;
  };
}

// 上传相关
export interface UploadResponse {
  code: number;
  message: string;
  data: {
    url: string;
  };
}

// 查询参数
export interface ArticleQuery {
  page?: number;
  limit?: number;
  order?: string;
  keyword?: string;
  status?: string;
  visibility?: string;
  author?: string;
  category?: string;
  tag?: string;
}

// 设置相关
export interface BasicSettings {
  siteName: string;
  logo: string;
  favicon: string;
  siteDescription: string;
  siteKeywords: string;
  siteFooter: string;
}

export interface SettingsResponse {
  code: number;
  message: string;
  data: {
    settings: {
      basic: BasicSettings;
    };
  };
}

// 表单相关
export interface FormState {
  username: string;
  nickname: string;
  email: string;
  avatar: string;
  bio: string;
}

export interface ArticleForm {
  title: string;
  content: string;
  summary?: string;
  cover_image?: string;
  category?: string | null;
  tags: string[];
  status: 'draft' | 'published' | 'archived';
  visibility: 'public' | 'private' | 'custom' | 'group';
  visible_to: string[];
  visible_groups: string[];
} 