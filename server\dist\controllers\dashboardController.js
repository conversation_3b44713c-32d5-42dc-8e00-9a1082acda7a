"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStats = void 0;
const sequelize_1 = require("sequelize");
const Article_1 = __importDefault(require("../models/Article"));
const User_1 = __importDefault(require("../models/User"));
const Category_1 = __importDefault(require("../models/Category"));
const Tag_1 = __importDefault(require("../models/Tag"));
// @desc    获取仪表盘统计数据
// @route   GET /api/dashboard/stats
// @access  Private
const getStats = async (req, res, next) => {
    try {
        // 获取文章统计
        const totalArticles = await Article_1.default.count();
        const publishedArticles = await Article_1.default.count({ where: { status: 'published' } });
        const draftArticles = await Article_1.default.count({ where: { status: 'draft' } });
        const archivedArticles = await Article_1.default.count({ where: { status: 'archived' } });
        // 获取用户统计
        const userCount = await User_1.default.count();
        const activeUserCount = await User_1.default.count({ where: { is_active: true } });
        // 获取分类统计
        const categoryCount = await Category_1.default.count();
        // 获取标签统计
        const tagCount = await Tag_1.default.count();
        // 获取过去7天的文章发布趋势
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        const articleTrend = [];
        for (let i = 0; i < 7; i++) {
            const date = new Date(sevenDaysAgo);
            date.setDate(date.getDate() + i);
            const startOfDay = new Date(date.setHours(0, 0, 0, 0));
            const endOfDay = new Date(date.setHours(23, 59, 59, 999));
            const count = await Article_1.default.count({
                where: {
                    created_at: {
                        [sequelize_1.Op.gte]: startOfDay,
                        [sequelize_1.Op.lte]: endOfDay
                    },
                    status: 'published'
                }
            });
            articleTrend.push({
                date: startOfDay.toISOString().split('T')[0],
                count
            });
        }
        // 获取最近的文章
        const recentArticles = await Article_1.default.findAll({
            include: [
                { model: User_1.default, as: 'author', attributes: ['username'] },
                { model: Category_1.default, as: 'category', attributes: ['name'] }
            ],
            order: [['created_at', 'DESC']],
            limit: 5,
            attributes: ['id', 'title', 'status', 'view_count', 'created_at']
        });
        // 获取热门文章
        const popularArticles = await Article_1.default.findAll({
            include: [
                { model: User_1.default, as: 'author', attributes: ['username'] },
                { model: Category_1.default, as: 'category', attributes: ['name'] }
            ],
            order: [['view_count', 'DESC']],
            limit: 5,
            attributes: ['id', 'title', 'status', 'view_count', 'created_at']
        });
        // 格式化文章统计数据
        const formattedArticleStats = {
            total: totalArticles,
            published: publishedArticles,
            draft: draftArticles,
            archived: archivedArticles
        };
        res.status(200).json({
            code: 200,
            message: '获取仪表盘数据成功',
            data: {
                articles: formattedArticleStats,
                articleTrend,
                users: {
                    total: userCount,
                    active: activeUserCount
                },
                categories: {
                    total: categoryCount
                },
                tags: {
                    total: tagCount
                },
                recentArticles,
                popularArticles
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getStats = getStats;
