"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const app_1 = __importDefault(require("./app"));
const db_1 = __importDefault(require("./config/db"));
// 加载环境变量
dotenv_1.default.config();
// 连接数据库并启动服务器
const startServer = async () => {
    try {
        // 连接MySQL数据库
        await (0, db_1.default)();
        // 启动服务器
        const port = Number(process.env.PORT) || 3000;
        app_1.default.listen(port, '0.0.0.0', () => {
            console.log(`服务器运行在 http://0.0.0.0:${port}`);
            console.log(`本地访问: http://localhost:${port}`);
            console.log('环境:', process.env.NODE_ENV);
            console.log('数据库:', process.env.DB_NAME || 'admin_system');
        });
    }
    catch (error) {
        console.error('服务器启动失败:', error);
        process.exit(1);
    }
};
startServer();
