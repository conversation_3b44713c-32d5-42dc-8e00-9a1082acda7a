"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const db_1 = require("../config/db");
const models_1 = require("../models");
// 加载环境变量
dotenv_1.default.config({ path: path_1.default.resolve(__dirname, '../../.env') });
async function createAdminUser() {
    try {
        console.log('正在连接到MySQL数据库...');
        // 连接到MySQL
        await db_1.sequelize.authenticate();
        (0, models_1.setupAssociations)();
        await db_1.sequelize.sync({ alter: true });
        console.log('MySQL连接成功');
        // 检查是否已存在管理员账号
        const existingAdmin = await models_1.User.findOne({ where: { role: 'admin' } });
        if (existingAdmin) {
            console.log('管理员账号已存在:', existingAdmin.username);
            await db_1.sequelize.close();
            return;
        }
        // 创建默认管理员账号
        const newAdmin = await models_1.User.create({
            username: 'admin',
            password: 'admin123', // 会自动加密
            email: '<EMAIL>',
            role: 'admin',
            nickname: '系统管理员',
            is_active: true
        });
        console.log('默认管理员账号创建成功:');
        console.log('用户名: admin');
        console.log('密码: admin123');
        console.log('邮箱: <EMAIL>');
        console.log('请登录后立即修改默认密码!');
        // 断开数据库连接
        await db_1.sequelize.close();
        console.log('MySQL连接已关闭');
    }
    catch (error) {
        console.error('创建管理员账号失败:', error);
        process.exit(1);
    }
}
// 执行创建管理员账号的函数
createAdminUser();
