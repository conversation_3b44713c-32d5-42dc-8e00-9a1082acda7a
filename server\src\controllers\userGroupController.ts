import { Request, Response, NextFunction } from 'express';
import { Op } from 'sequelize';
import UserGroup from '../models/UserGroup';
import User from '../models/User';
import { UserGroupMember } from '../models/associations';
import AppError from '../utils/appError';
import { catchAsync } from '../utils/catchAsync';
import logger from '../utils/logger';

// 获取所有用户组
export const getAllUserGroups = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { page = 1, limit = 10, search } = req.query;
    const pageNum = parseInt(page as string);
    const pageSize = parseInt(limit as string);
    const offset = (pageNum - 1) * pageSize;

    // 构建查询条件
    const whereClause: any = {};
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows: userGroups } = await UserGroup.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'members',
          attributes: ['id', 'username', 'nickname', 'email', 'avatar'],
          through: { attributes: [] }
        }
      ],
      order: [['created_at', 'DESC']],
      offset,
      limit: pageSize
    });

    res.status(200).json({
      code: 200,
      message: '获取用户组列表成功',
      data: {
        total: count,
        items: userGroups,
        page: pageNum,
        pageSize,
        totalPages: Math.ceil(count / pageSize)
      }
    });
  } catch (error: any) {
    logger.error('获取用户组列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取用户组列表失败',
      error: error.message
    });
  }
});

// 获取单个用户组
export const getUserGroup = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const userGroup = await UserGroup.findByPk(id, {
      include: [
        {
          model: User,
          as: 'members',
          attributes: ['id', 'username', 'nickname', 'email', 'avatar'],
          through: { attributes: [] }
        }
      ]
    });

    if (!userGroup) {
      return next(new AppError('用户组不存在', 404));
    }

    res.status(200).json({
      code: 200,
      message: '获取用户组成功',
      data: userGroup
    });
  } catch (error: any) {
    logger.error('获取用户组失败:', error);
    return next(new AppError('获取用户组失败', 500));
  }
});

// 创建用户组
export const createUserGroup = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { name, description, memberIds = [] } = req.body;

    // 检查用户组名是否已存在
    const existingGroup = await UserGroup.findOne({ where: { name } });
    if (existingGroup) {
      return next(new AppError('用户组名称已存在', 400));
    }

    // 创建用户组
    const userGroup = await UserGroup.create({
      name,
      description
    });

    // 添加成员
    if (memberIds && memberIds.length > 0) {
      const users = await User.findAll({
        where: { id: { [Op.in]: memberIds } }
      });
      await (userGroup as any).setMembers(users);
    }

    // 获取完整的用户组信息
    const fullUserGroup = await UserGroup.findByPk(userGroup.id, {
      include: [
        {
          model: User,
          as: 'members',
          attributes: ['id', 'username', 'nickname', 'email', 'avatar'],
          through: { attributes: [] }
        }
      ]
    });

    res.status(201).json({
      code: 201,
      message: '创建用户组成功',
      data: fullUserGroup
    });
  } catch (error: any) {
    logger.error('创建用户组失败:', error);
    return next(new AppError('创建用户组失败', 500));
  }
});

// 更新用户组
export const updateUserGroup = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { name, description, memberIds } = req.body;

    const userGroup = await UserGroup.findByPk(id);
    if (!userGroup) {
      return next(new AppError('用户组不存在', 404));
    }

    // 更新基本信息
    await userGroup.update({ name, description });

    // 更新成员
    if (memberIds !== undefined) {
      const users = await User.findAll({
        where: { id: { [Op.in]: memberIds } }
      });
      await (userGroup as any).setMembers(users);
    }

    // 获取更新后的完整信息
    const updatedUserGroup = await UserGroup.findByPk(id, {
      include: [
        {
          model: User,
          as: 'members',
          attributes: ['id', 'username', 'nickname', 'email', 'avatar'],
          through: { attributes: [] }
        }
      ]
    });

    res.status(200).json({
      code: 200,
      message: '更新用户组成功',
      data: updatedUserGroup
    });
  } catch (error: any) {
    logger.error('更新用户组失败:', error);
    return next(new AppError('更新用户组失败', 500));
  }
});

// 删除用户组
export const deleteUserGroup = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const userGroup = await UserGroup.findByPk(id);
    if (!userGroup) {
      return next(new AppError('用户组不存在', 404));
    }

    await userGroup.destroy();

    res.status(200).json({
      code: 200,
      message: '删除用户组成功'
    });
  } catch (error: any) {
    logger.error('删除用户组失败:', error);
    return next(new AppError('删除用户组失败', 500));
  }
});

// 添加成员到用户组
export const addMemberToGroup = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { groupId, userId } = req.params;

    const userGroup = await UserGroup.findByPk(groupId);
    if (!userGroup) {
      return next(new AppError('用户组不存在', 404));
    }

    const user = await User.findByPk(userId);
    if (!user) {
      return next(new AppError('用户不存在', 404));
    }

    // 检查用户是否已经在组中
    const existingMember = await UserGroupMember.findOne({
      where: { user_group_id: groupId, user_id: userId }
    });

    if (existingMember) {
      return next(new AppError('用户已经在该组中', 400));
    }

    await UserGroupMember.create({
      user_group_id: parseInt(groupId),
      user_id: parseInt(userId)
    });

    res.status(200).json({
      code: 200,
      message: '添加成员成功'
    });
  } catch (error: any) {
    logger.error('添加成员失败:', error);
    return next(new AppError('添加成员失败', 500));
  }
});

// 从用户组移除成员
export const removeMemberFromGroup = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { groupId, userId } = req.params;

    const member = await UserGroupMember.findOne({
      where: { user_group_id: groupId, user_id: userId }
    });

    if (!member) {
      return next(new AppError('用户不在该组中', 404));
    }

    await member.destroy();

    res.status(200).json({
      code: 200,
      message: '移除成员成功'
    });
  } catch (error: any) {
    logger.error('移除成员失败:', error);
    return next(new AppError('移除成员失败', 500));
  }
});

// 获取用户所属的用户组
export const getUserGroups = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { userId } = req.params;

    const user = await User.findByPk(userId, {
      include: [
        {
          model: UserGroup,
          as: 'groups',
          attributes: ['id', 'name', 'description'],
          through: { attributes: [] }
        }
      ]
    });

    if (!user) {
      return next(new AppError('用户不存在', 404));
    }

    res.status(200).json({
      code: 200,
      message: '获取用户组成功',
      data: (user as any).groups || []
    });
  } catch (error: any) {
    logger.error('获取用户组失败:', error);
    return next(new AppError('获取用户组失败', 500));
  }
});
