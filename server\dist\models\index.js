"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticleVisibleGroup = exports.ArticleVisibleUser = exports.ArticleTag = exports.UserGroupMember = exports.Announcement = exports.Setting = exports.Article = exports.UserGroup = exports.Tag = exports.Category = exports.User = void 0;
exports.setupAssociations = setupAssociations;
// 导入所有模型
const User_1 = __importDefault(require("./User"));
exports.User = User_1.default;
const Category_1 = __importDefault(require("./Category"));
exports.Category = Category_1.default;
const Tag_1 = __importDefault(require("./Tag"));
exports.Tag = Tag_1.default;
const UserGroup_1 = __importDefault(require("./UserGroup"));
exports.UserGroup = UserGroup_1.default;
const Article_1 = __importDefault(require("./Article"));
exports.Article = Article_1.default;
const Setting_1 = __importDefault(require("./Setting"));
exports.Setting = Setting_1.default;
const Announcement_1 = __importDefault(require("./Announcement"));
exports.Announcement = Announcement_1.default;
const associations_1 = require("./associations");
Object.defineProperty(exports, "UserGroupMember", { enumerable: true, get: function () { return associations_1.UserGroupMember; } });
Object.defineProperty(exports, "ArticleTag", { enumerable: true, get: function () { return associations_1.ArticleTag; } });
Object.defineProperty(exports, "ArticleVisibleUser", { enumerable: true, get: function () { return associations_1.ArticleVisibleUser; } });
Object.defineProperty(exports, "ArticleVisibleGroup", { enumerable: true, get: function () { return associations_1.ArticleVisibleGroup; } });
// 设置模型关联关系
function setupAssociations() {
    // User 和 UserGroup 的多对多关系
    User_1.default.belongsToMany(UserGroup_1.default, {
        through: associations_1.UserGroupMember,
        foreignKey: 'user_id',
        otherKey: 'user_group_id',
        as: 'groups'
    });
    UserGroup_1.default.belongsToMany(User_1.default, {
        through: associations_1.UserGroupMember,
        foreignKey: 'user_group_id',
        otherKey: 'user_id',
        as: 'members'
    });
    // User 和 Article 的一对多关系
    User_1.default.hasMany(Article_1.default, {
        foreignKey: 'author_id',
        as: 'articles'
    });
    Article_1.default.belongsTo(User_1.default, {
        foreignKey: 'author_id',
        as: 'author'
    });
    // Category 和 Article 的一对多关系
    Category_1.default.hasMany(Article_1.default, {
        foreignKey: 'category_id',
        as: 'articles'
    });
    Article_1.default.belongsTo(Category_1.default, {
        foreignKey: 'category_id',
        as: 'category'
    });
    // Article 和 Tag 的多对多关系
    Article_1.default.belongsToMany(Tag_1.default, {
        through: associations_1.ArticleTag,
        foreignKey: 'article_id',
        otherKey: 'tag_id',
        as: 'tags'
    });
    Tag_1.default.belongsToMany(Article_1.default, {
        through: associations_1.ArticleTag,
        foreignKey: 'tag_id',
        otherKey: 'article_id',
        as: 'articles'
    });
    // Article 和 User 的可见性多对多关系
    Article_1.default.belongsToMany(User_1.default, {
        through: associations_1.ArticleVisibleUser,
        foreignKey: 'article_id',
        otherKey: 'user_id',
        as: 'visible_to'
    });
    // Article 和 UserGroup 的可见性多对多关系
    Article_1.default.belongsToMany(UserGroup_1.default, {
        through: associations_1.ArticleVisibleGroup,
        foreignKey: 'article_id',
        otherKey: 'user_group_id',
        as: 'visible_groups'
    });
}
// 导出默认模型对象
exports.default = {
    User: User_1.default,
    Category: Category_1.default,
    Tag: Tag_1.default,
    UserGroup: UserGroup_1.default,
    Article: Article_1.default,
    Setting: Setting_1.default,
    Announcement: Announcement_1.default,
    UserGroupMember: associations_1.UserGroupMember,
    ArticleTag: associations_1.ArticleTag,
    ArticleVisibleUser: associations_1.ArticleVisibleUser,
    ArticleVisibleGroup: associations_1.ArticleVisibleGroup,
    setupAssociations
};
