<template>
  <div class="article-edit-page">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-title">
        <FileTextOutlined /> {{ isEdit ? '编辑文章' : '写文章' }}
      </div>
      <div class="header-actions">
        <a-space>
          <a-button @click="handleSaveDraft">
            <template #icon><SaveOutlined /></template>
            保存草稿
          </a-button>
          <a-button type="primary" @click="handlePublish">
            <template #icon><SendOutlined /></template>
            发布文章
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 编辑表单 -->
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      layout="vertical"
      class="edit-form"
    >
      <a-row :gutter="16">
        <a-col :span="16">
          <!-- 主要内容区 -->
          <a-card :bordered="false" class="content-card">
            <a-form-item name="title" label="文章标题">
              <a-input
                v-model:value="formState.title"
                placeholder="请输入文章标题"
                :maxLength="200"
                show-count
                size="large"
                @change="handleTitleChange"
              />
            </a-form-item>

            <a-form-item name="slug" label="文章URL" extra="自定义URL，留空将根据标题自动生成">
              <a-input
                v-model:value="formState.slug"
                placeholder="自定义文章URL，留空将根据标题自动生成"
                :maxLength="100"
                addonBefore="/article/"
              />
            </a-form-item>

            <a-form-item name="content" label="文章内容">
              <div class="editor-wrapper">
                <TinyEditor
                  ref="editorRef"
                  v-model="formState.content"
                  :height="700"
                  :placeholder="'请输入文章内容...'"
                  @editorInit="handleEditorInit"
                />
              </div>
            </a-form-item>
          </a-card>
        </a-col>

        <a-col :span="8">
          <!-- 设置面板 -->
          <a-card :bordered="false" class="settings-card" title="文章设置">
            <a-form-item name="category" label="文章分类">
              <a-select
                v-model:value="formState.category"
                placeholder="请选择分类"
                :loading="categoriesLoading"
                @change="handleCategorySelect"
              >
                <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item name="file_upload" label="文件上传">
              <a-space>
                <a-button @click="handleInsertWord" class="file-btn">
                  <span class="file-icon">📄</span>
                  上传Word
                </a-button>
                <a-button @click="handleInsertPDF" class="file-btn">
                  <span class="file-icon">📑</span>
                  上传PDF
                </a-button>
                <a-button @click="handleInsertVideo" class="file-btn">
                  <span class="file-icon">🎬</span>
                  上传视频
                </a-button>
              </a-space>
            </a-form-item>

            <a-form-item name="tags" label="文章标签">
              <a-select
                v-model:value="formState.tags"
                mode="multiple"
                placeholder="请选择标签"
                :loading="tagsLoading"
                @change="handleTagsChange"
              >
                <a-select-option v-for="tag in tags" :key="tag.id" :value="tag.id">
                  {{ tag.name }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item name="summary" label="文章摘要">
              <a-textarea
                v-model:value="formState.summary"
                placeholder="请输入文章摘要"
                :rows="4"
                :maxLength="500"
                show-count
              />
            </a-form-item>

            <a-form-item name="status" label="文章状态">
              <a-radio-group v-model:value="formState.status">
                <a-radio-button value="draft">草稿</a-radio-button>
                <a-radio-button value="published">发布</a-radio-button>
                <a-radio-button value="archived">归档</a-radio-button>
              </a-radio-group>
            </a-form-item>

            <a-form-item name="visibility" label="文章可见性">
              <a-radio-group v-model:value="formState.visibility">
                <a-radio-button value="public">公开</a-radio-button>
                <a-radio-button value="private">私密</a-radio-button>
                <a-radio-button value="custom">指定用户可见</a-radio-button>
                <a-radio-button value="group">用户组可见</a-radio-button>
              </a-radio-group>
            </a-form-item>

            <a-form-item v-if="formState.visibility === 'custom'" name="visible_to" label="可见用户">
              <a-select
                v-model:value="formState.visible_to"
                mode="multiple"
                placeholder="请选择可见用户"
                :loading="usersLoading"
                show-search
                :filter-option="false"
                @search="handleUserSearch"
                @change="handleVisibleUsersChange"
              >
                <a-select-option v-for="user in userOptions" :key="user.value" :value="user.value">
                  {{ user.label }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item v-if="formState.visibility === 'group'" name="visible_groups" label="可见用户组">
              <a-select
                v-model:value="formState.visible_groups"
                mode="multiple"
                placeholder="请选择可见用户组"
                :loading="userGroupsLoading"
              >
                <a-select-option v-for="group in userGroups" :key="group.id" :value="group.id">
                  {{ group.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-card>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { FormInstance } from 'ant-design-vue'
import {
  FileTextOutlined,
  SaveOutlined,
  SendOutlined,
  PlusOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { UploadProps } from 'ant-design-vue'
import TinyEditor from '@/components/TinyEditor.vue'
import articleApi from '@/api/modules/article'
import categoryApi from '@/api/modules/category'
import tagApi from '@/api/modules/tag'
import userApi from '@/api/modules/user'
import { debounce } from 'lodash'
import { uploadApi } from '@/api'
import userGroupApi from '@/api/modules/userGroup'

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()

// 判断是否是编辑模式
const isEdit = computed(() => route.params.id !== undefined)

// 表单状态
const formState = ref({
  title: '',
  content: '',
  summary: '',
  status: 'draft',
  visibility: 'public',
  visible_to: [] as number[],
  visible_groups: [] as number[],
  category: undefined as number | undefined,
  tags: [] as number[],
  slug: ''
})

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入文章标题' }],
  content: [{ required: true, message: '请输入文章内容' }],
  category: [{ required: true, message: '请选择文章分类', type: 'number' }],
  visible_to: [{
    required: true,
    message: '请选择可见用户',
    type: 'array',
    validator: (rule: any, value: number[]) => {
      if (formState.value.visibility === 'custom' && (!value || value.length === 0)) {
        return Promise.reject('请选择可见用户')
      }
      return Promise.resolve()
    }
  }],
  visible_groups: [{
    required: true,
    message: '请选择可见用户组',
    type: 'array',
    validator: (rule: any, value: number[]) => {
      if (formState.value.visibility === 'group' && (!value || value.length === 0)) {
        return Promise.reject('请选择可见用户组')
      }
      return Promise.resolve()
    }
  }]
}

// 分类和标签数据
const categories = ref<any[]>([])
const tags = ref<any[]>([])

// 用户选择相关
const usersLoading = ref(false)
const userOptions = ref<{ value: number; label: string; }[]>([])
const userGroupsLoading = ref(false)
const userGroups = ref<any[]>([])

// 加载状态
const categoriesLoading = ref(false)
const tagsLoading = ref(false)

// 在 script setup 中添加
const editorRef = ref(null)

// 处理编辑器初始化
const handleEditorInit = (editor) => {
  editorRef.value = editor
  console.log('TinyMCE编辑器初始化完成')

  // 添加保存快捷键
  editor.addShortcut('meta+s', '保存文章', () => {
    handleSaveDraft()
  })
}

// 处理Word文件插入
const handleInsertWord = async () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.doc,.docx'
  input.onchange = async (e: Event) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        const res = await uploadApi.uploadFile(formData)
        if (res.code === 0 && res.data?.url) {
          const fileUrl = res.data.url
          const fileName = file.name
          // 插入文件链接到编辑器
          if (editorRef.value) {
            editorRef.value.insertContent(`<p><a href="${fileUrl}" target="_blank">📄 ${fileName}</a></p>`)
            message.success('Word文件上传成功')
          }
        } else {
          throw new Error(res.message || 'Word文件上传失败')
        }
      } catch (error: any) {
        console.error('Word文件上传失败:', error)
        message.error(error.message || 'Word文件上传失败')
      }
    }
  }
  input.click()
}

// 处理PDF文件插入
const handleInsertPDF = async () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.pdf'
  input.onchange = async (e: Event) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        const res = await uploadApi.uploadFile(formData)
        if (res.code === 0 && res.data?.url) {
          const fileUrl = res.data.url
          const fileName = file.name
          // 插入文件链接到编辑器
          if (editorRef.value) {
            editorRef.value.insertContent(`<p><a href="${fileUrl}" target="_blank">📑 ${fileName}</a></p>`)
            message.success('PDF文件上传成功')
          }
        } else {
          throw new Error(res.message || 'PDF文件上传失败')
        }
      } catch (error: any) {
        console.error('PDF文件上传失败:', error)
        message.error(error.message || 'PDF文件上传失败')
      }
    }
  }
  input.click()
}

// 处理视频文件插入
const handleInsertVideo = async () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.mp4,.webm,.ogg,.mov,.avi'
  input.onchange = async (e: Event) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      try {
        const formData = new FormData()
        formData.append('video', file)
        const res = await uploadApi.uploadVideo(formData)
        if (res.code === 0 && res.data?.url) {
          const videoUrl = res.data.url
          // 富文本编辑器中插入视频
          if (editorRef.value) {
            // 获取编辑器实例
            const editor = editorRef.value
            // 在当前光标位置插入视频
            editor.insertContent(`<video controls width="100%" src="${videoUrl}"></video>`)
            message.success('视频上传成功')
          }
        } else {
          throw new Error(res.message || '视频上传失败')
        }
      } catch (error: any) {
        console.error('视频上传失败:', error)
        message.error(error.message || '视频上传失败')
      }
    }
  }
  input.click()
}

// 加载文章数据
const fetchArticle = async (id: string) => {
  try {
    const res = await articleApi.getArticle(id)
    const article = res.data
    formState.value = {
      title: article.title,
      content: article.content,
      summary: article.summary || '',
      status: article.status,
      visibility: article.visibility || 'public',
      visible_to: article.visible_to?.map((u: any) => u.id) || [],
      visible_groups: article.visible_groups?.map((g: any) => g.id) || [],
      category: article.category?.id || undefined,
      tags: article.tags?.map((t: any) => t.id) || [],
      slug: article.slug
    }

    // 如果有指定可见用户，加载这些用户的信息
    if (article.visible_to?.length > 0) {
      const selectedUsers = article.visible_to.map((user: any) => ({
        value: user.id,
        label: `${user.username} (${user.email})`
      }))
      userOptions.value = selectedUsers
    }

    // 如果有指定可见用户组，加载这些用户组的信息
    if (article.visible_groups?.length > 0) {
      const selectedGroups = article.visible_groups.map((group: any) => ({
        value: group.id,
        label: group.name
      }))
      userGroups.value = selectedGroups
    }
  } catch (error) {
    console.error('获取文章失败:', error)
    message.error('获取文章失败')
  }
}

// 加载分类列表
const fetchCategories = async () => {
  categoriesLoading.value = true
  try {
    const res = await categoryApi.getCategories()
    categories.value = res.data || []
  } catch (error) {
    console.error('获取分类列表失败:', error)
    message.error('获取分类列表失败')
  } finally {
    categoriesLoading.value = false
  }
}

// 加载标签列表
const fetchTags = async () => {
  tagsLoading.value = true
  try {
    const res = await tagApi.getTags()
    tags.value = res.data || []
  } catch (error) {
    console.error('获取标签列表失败:', error)
    message.error('获取标签列表失败')
  } finally {
    tagsLoading.value = false
  }
}

// 加载用户列表
const fetchUsers = async (search?: string) => {
  usersLoading.value = true;
  try {
    const res = await userApi.getUsers({
      search,
      pageSize: 20,
      isAdmin: true
    });

    if (res.code === 0 || res.code === 200) {
      const users = res.data.items || [];
      const newOptions = users.map(user => ({
        value: user.id,
        label: `${user.username} (${user.email})`
      }));

      if (!search) {
        userOptions.value = newOptions;
      } else {
        // 合并现有选项和搜索结果
        const existingValues = formState.value.visible_to;
        const existingOptions = userOptions.value.filter(option =>
          existingValues.includes(option.value)
        );
        userOptions.value = [...existingOptions, ...newOptions.filter(option =>
          !existingValues.includes(option.value)
        )];
      }
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  } finally {
    usersLoading.value = false;
  }
};

// 加载用户组列表
const fetchUserGroups = async () => {
  userGroupsLoading.value = true;
  try {
    console.log('开始获取用户组列表...');
    const res = await userGroupApi.getUserGroups();
    console.log('用户组列表响应:', res);

    if (res.code === 0 || res.code === 200) {
      let groupsData = [];

      // 处理分页数据格式
      if (res.data && res.data.items) {
        groupsData = res.data.items;
      } else if (Array.isArray(res.data)) {
        groupsData = res.data;
      }

      userGroups.value = groupsData.map(group => ({
        id: group.id,
        name: group.name,
        description: group.description
      }));
      console.log('处理后的用户组:', userGroups.value);
    } else {
      console.error('获取用户组列表失败:', res);
      message.error(res.message || '获取用户组列表失败');
    }
  } catch (error: any) {
    console.error('获取用户组列表失败:', {
      error,
      response: error.response,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    message.error('获取用户组列表失败');
  } finally {
    userGroupsLoading.value = false;
  }
};

// 保存草稿
const handleSaveDraft = () => {
  formState.value.status = 'draft'
  handleSubmit()
}

// 发布文章
const handlePublish = () => {
  formState.value.status = 'published'
  handleSubmit()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    const data = {
      ...formState.value,
      summary: formState.value.summary || formState.value.content.substring(0, 200),
      category: formState.value.category as number, // 确保category是数字
      visible_to: formState.value.visibility === 'custom' ? formState.value.visible_to : undefined,
      visible_groups: formState.value.visibility === 'group' ? formState.value.visible_groups : undefined
    }

    if (isEdit.value) {
      await articleApi.updateArticle(route.params.id as string, data)
      message.success('文章更新成功')
    } else {
      await articleApi.createArticle(data)
      message.success('文章创建成功')
    }

    router.push('/articles')
  } catch (error) {
    console.error('保存文章失败:', error)
    message.error('保存文章失败')
  }
}

// 处理用户搜索
const handleUserSearch = debounce((value: string) => {
  if (value) {
    fetchUsers(value)
  } else {
    // 当搜索框清空时，重新加载初始列表
    fetchUsers()
  }
}, 300)

// 监听可见性变化
watch(() => formState.value.visibility, (newValue) => {
  if (newValue === 'custom') {
    fetchUsers()
  } else if (newValue === 'group') {
    fetchUserGroups()
  } else {
    formState.value.visible_to = []
    formState.value.visible_groups = []
    userOptions.value = []
    userGroups.value = []
  }
})

// 处理用户选择
const handleUserSelect = (value: number) => {
  const currentSelected = formState.value.visible_to
  if (!currentSelected.includes(value)) {
    formState.value.visible_to = [...currentSelected, value]
  }
}

// 处理分类选择
const handleCategorySelect = (value: number) => {
  formState.value.category = value
}

// 处理标签变化
const handleTagsChange = (value: number[]) => {
  formState.value.tags = value
}

// 处理可见用户变化
const handleVisibleUsersChange = (value: number[]) => {
  formState.value.visible_to = value
}

// 处理标题变化
const handleTitleChange = (e: Event) => {
  // 只有当用户没有手动设置slug时，才自动生成
  if (!formState.value.slug || formState.value.slug === '') {
    const title = (e.target as HTMLInputElement).value
    // 简单的slug生成逻辑，实际应用中可以使用更复杂的算法
    if (title) {
      const slug = title
        .toLowerCase()
        .trim()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')

      // 如果生成的slug不为空，则使用它
      if (slug) {
        formState.value.slug = slug
      }
    }
  }
}

// 初始化
onMounted(async () => {
  await Promise.all([fetchCategories(), fetchTags()])
  if (isEdit.value) {
    await fetchArticle(route.params.id as string)
  }
  // 如果是编辑模式且有可见用户组，或者是新建模式且选择了用户组可见
  if ((isEdit.value && formState.value.visible_groups.length > 0) ||
      (!isEdit.value && formState.value.visibility === 'group')) {
    await fetchUserGroups()
  }
})
</script>

<style scoped>
.article-edit-page {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-title {
  font-size: 20px;
  font-weight: 500;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-form {
  margin-top: 16px;
}

.content-card,
.settings-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.editor-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  height: 600px;
}

:deep(.md-editor) {
  border: none;
  height: 100%;

  .md-editor-content {
    height: calc(100% - 42px);

    :deep(.cm-editor) {
      font-size: 16px;
      line-height: 1.6;
    }

    :deep(.cm-line) {
      padding: 2px 8px;
    }
  }
}

/* 修复工具栏按钮样式 */
:deep(.md-editor-toolbar) {
  display: flex;
  flex-wrap: wrap;
  padding: 6px;
  background-color: #f9f9f9;

  .md-editor-toolbar-item {
    margin: 3px;
    padding: 4px;
    font-size: 18px;
    display: inline-flex !important;
    align-items: center;
    justify-content: center;

    button {
      width: 30px;
      height: 30px;
      padding: 4px;
      border-radius: 3px;
      display: flex !important;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: rgba(0, 0, 0, 0.08);
      }
    }

    svg {
      width: 18px;
      height: 18px;
    }

    &.insert-word,
    &.insert-pdf,
    &.image {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;

      button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: auto;
        min-width: 30px;
        height: 30px;
      }
    }
  }
}

:deep(.ant-upload-select) {
  width: 100% !important;
  height: 160px !important;
  margin-inline-end: 0 !important;
}

:deep(.ant-upload-list-item) {
  width: 100% !important;
  height: 160px !important;
  margin-inline-end: 0 !important;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-select-multiple .ant-select-selection-placeholder) {
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.ant-select-dropdown) {
  max-height: 300px;
  overflow-y: auto;
}

.file-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
  margin-right: 8px;
  height: 32px;
  padding: 0 12px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.file-icon {
  font-size: 16px;
  margin-right: 6px;
}
</style>
