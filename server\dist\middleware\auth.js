"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorize = exports.protect = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = __importDefault(require("../models/User"));
const error_1 = require("./error");
const logger_1 = __importDefault(require("../utils/logger"));
// 保护路由中间件
const protect = async (req, res, next) => {
    try {
        let token;
        logger_1.default.info('验证请求头:', {
            authHeader: req.headers.authorization,
            path: req.path,
            method: req.method,
            query: req.query,
            cookies: req.cookies
        });
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
            token = req.headers.authorization.split(' ')[1];
            logger_1.default.info('获取到 token:', {
                token: token.substring(0, 10) + '...',
                tokenLength: token.length
            });
            try {
                // 验证 token 并指定类型
                const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'blogadminsecretkey2024');
                logger_1.default.info('token 解析结果:', { decoded });
                // 获取用户信息
                const user = await User_1.default.findByPk(decoded.id, {
                    attributes: { exclude: ['password'] }
                });
                if (!user) {
                    logger_1.default.warn('token 有效但用户不存在');
                    return res.status(401).json({
                        code: 401,
                        message: '用户不存在'
                    });
                }
                // 将用户信息添加到请求对象
                req.user = user;
                logger_1.default.info('用户验证成功:', {
                    userId: user.id,
                    role: user.role,
                    username: user.username
                });
            }
            catch (error) {
                logger_1.default.error('token 验证失败:', error);
                // token 验证失败时不返回错误，继续处理请求
                logger_1.default.info('继续处理请求，作为未登录用户');
            }
        }
        else {
            logger_1.default.info('未提供 token，继续处理请求，作为未登录用户');
        }
        next();
    }
    catch (error) {
        logger_1.default.error('认证中间件错误:', error);
        next(error);
    }
};
exports.protect = protect;
// 角色授权中间件
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return next(new error_1.ErrorResponse('请先登录', 401));
        }
        if (!roles.includes(req.user.role)) {
            return next(new error_1.ErrorResponse('无权限访问此资源', 403));
        }
        next();
    };
};
exports.authorize = authorize;
