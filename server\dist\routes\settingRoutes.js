"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const settingController_1 = require("../controllers/settingController");
const auth_1 = require("../middleware/auth");
const logger_1 = __importDefault(require("../utils/logger"));
const Setting_1 = __importDefault(require("../models/Setting"));
const router = express_1.default.Router();
// 获取设置 - 公开访问
router.get('/:group', settingController_1.getSettings);
// 调试路由 - 获取所有设置
router.get('/', async (req, res) => {
    try {
        logger_1.default.info('获取所有设置');
        const settings = await Setting_1.default.findAll();
        const settingsObject = settings.reduce((acc, setting) => {
            if (!acc[setting.group]) {
                acc[setting.group] = {};
            }
            acc[setting.group][setting.key] = setting.value;
            return acc;
        }, {});
        res.json({
            code: 0,
            message: '获取所有设置成功',
            data: {
                settings: settingsObject
            }
        });
    }
    catch (error) {
        logger_1.default.error('获取所有设置失败:', error);
        res.status(500).json({
            code: 500,
            message: '获取所有设置失败',
            error: error.message
        });
    }
});
// 更新设置 - 需要管理员权限
router.put('/', auth_1.protect, (0, auth_1.authorize)('admin'), settingController_1.updateSettings);
// 初始化默认设置（需要管理员权限）
router.post('/initialize', auth_1.protect, (0, auth_1.authorize)('admin'), settingController_1.initializeSettings);
// 开发环境公开初始化接口 - 无需验证
// 无论当前环境如何，都提供此端点，以便测试和调试
router.post('/dev-initialize', async (req, res) => {
    try {
        logger_1.default.info('使用公开端点初始化设置');
        // 复制初始化设置的代码
        const defaultSettings = [
            // 基本设置
            {
                group: 'basic',
                key: 'siteName',
                value: '我的博客',
                description: '网站名称'
            },
            {
                group: 'basic',
                key: 'logo',
                value: '',
                description: '网站 Logo'
            },
            {
                group: 'basic',
                key: 'favicon',
                value: '',
                description: '网站图标'
            },
            {
                group: 'basic',
                key: 'siteDescription',
                value: '一个简单的博客系统',
                description: '网站描述'
            },
            {
                group: 'basic',
                key: 'siteKeywords',
                value: '博客,技术,生活',
                description: '网站关键词'
            },
            {
                group: 'basic',
                key: 'siteFooter',
                value: '© 2024 我的博客',
                description: '网站页脚'
            },
            // 注册设置
            {
                group: 'registration',
                key: 'inviteCodeRequired',
                value: true,
                description: '是否需要邀请码注册'
            },
            {
                group: 'registration',
                key: 'inviteCode',
                value: 'COMPANY2024',
                description: '注册邀请码'
            },
            {
                group: 'registration',
                key: 'registrationEnabled',
                value: true,
                description: '是否开启注册功能'
            }
        ];
        // 使用循环逐个更新设置
        for (const setting of defaultSettings) {
            const [instance, created] = await Setting_1.default.findOrCreate({
                where: { group: setting.group, key: setting.key },
                defaults: setting
            });
            if (!created) {
                await instance.update(setting);
            }
        }
        // 获取更新后的设置
        const updatedSettings = await Setting_1.default.findAll();
        // 将设置转换为对象格式
        const settingsObject = updatedSettings.reduce((acc, setting) => {
            if (!acc[setting.group]) {
                acc[setting.group] = {};
            }
            acc[setting.group][setting.key] = setting.value;
            return acc;
        }, {});
        logger_1.default.info('公开端点初始化设置成功');
        res.json({
            code: 0,
            message: '公开端点初始化设置成功',
            data: {
                settings: settingsObject
            }
        });
    }
    catch (error) {
        logger_1.default.error('初始化设置失败:', error);
        res.status(500).json({
            code: 500,
            message: '初始化设置失败',
            error: error.message
        });
    }
});
exports.default = router;
