"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeSettings = exports.updateSettings = exports.getSettings = void 0;
const Setting_1 = __importDefault(require("../models/Setting"));
const logger_1 = __importDefault(require("../utils/logger"));
// 验证设置组
const validGroups = ['basic', 'security', 'log', 'announcement', 'registration'];
// 验证基本设置必填字段
const validateBasicSettings = (settings) => {
    const errors = [];
    if (!settings.siteName) {
        errors.push('网站名称不能为空');
    }
    return errors;
};
// @desc    获取设置
// @route   GET /api/settings/:group
// @access  Public/Private
const getSettings = async (req, res) => {
    try {
        const { group } = req.params;
        logger_1.default.info(`获取设置组: ${group}`);
        // 验证设置组
        if (group && !validGroups.includes(group)) {
            return res.status(400).json({
                code: 400,
                message: '无效的设置组'
            });
        }
        // 查找指定组的所有设置
        const settings = await Setting_1.default.findAll({ where: { group } });
        logger_1.default.info(`找到 ${settings.length} 个设置项`);
        // 将设置转换为对象格式
        const settingsObject = {};
        settings.forEach((setting) => {
            if (!settingsObject[setting.group]) {
                settingsObject[setting.group] = {};
            }
            settingsObject[setting.group][setting.key] = setting.value;
        });
        // 打印返回的设置对象，用于调试
        logger_1.default.info(`返回设置: ${JSON.stringify(settingsObject)}`);
        res.json({
            code: 0,
            message: '获取设置成功',
            data: {
                settings: settingsObject
            }
        });
    }
    catch (error) {
        logger_1.default.error('获取设置失败:', error);
        res.status(500).json({
            code: 500,
            message: '获取设置失败',
            error: error.message
        });
    }
};
exports.getSettings = getSettings;
// @desc    更新设置
// @route   PUT /api/settings
// @access  Private/Admin
const updateSettings = async (req, res) => {
    try {
        const { group, settings } = req.body;
        logger_1.default.info(`更新设置组: ${group}`, settings);
        // 验证参数
        if (!group || !settings || typeof settings !== 'object') {
            return res.status(400).json({
                code: 400,
                message: '无效的参数'
            });
        }
        // 验证设置组
        if (!validGroups.includes(group)) {
            return res.status(400).json({
                code: 400,
                message: '无效的设置组'
            });
        }
        // 验证基本设置必填字段
        if (group === 'basic') {
            const errors = validateBasicSettings(settings);
            if (errors.length > 0) {
                return res.status(400).json({
                    code: 400,
                    message: errors[0]
                });
            }
        }
        // 逐个更新设置
        for (const [key, value] of Object.entries(settings)) {
            const [instance, created] = await Setting_1.default.findOrCreate({
                where: { group, key },
                defaults: { group, key, value }
            });
            if (!created) {
                await instance.update({ value });
            }
        }
        // 获取更新后的设置
        const updatedSettings = await Setting_1.default.findAll({ where: { group } });
        const settingsObject = updatedSettings.reduce((acc, setting) => {
            if (!acc[setting.group]) {
                acc[setting.group] = {};
            }
            acc[setting.group][setting.key] = setting.value;
            return acc;
        }, {});
        res.json({
            code: 0,
            message: '更新设置成功',
            data: {
                settings: settingsObject
            }
        });
    }
    catch (error) {
        logger_1.default.error('更新设置失败:', error);
        res.status(500).json({
            code: 500,
            message: '更新设置失败',
            error: error.message
        });
    }
};
exports.updateSettings = updateSettings;
// @desc    初始化默认设置
// @route   POST /api/settings/initialize
// @access  Private/Admin
const initializeSettings = async (req, res) => {
    try {
        const defaultSettings = [
            // 基本设置
            {
                group: 'basic',
                key: 'siteName',
                value: '我的博客',
                description: '网站名称'
            },
            {
                group: 'basic',
                key: 'logo',
                value: '',
                description: '网站 Logo'
            },
            {
                group: 'basic',
                key: 'favicon',
                value: '',
                description: '网站图标'
            },
            {
                group: 'basic',
                key: 'siteDescription',
                value: '一个简单的博客系统',
                description: '网站描述'
            },
            {
                group: 'basic',
                key: 'siteKeywords',
                value: '博客,技术,生活',
                description: '网站关键词'
            },
            {
                group: 'basic',
                key: 'siteFooter',
                value: '© 2024 我的博客',
                description: '网站页脚'
            },
            // 注册设置
            {
                group: 'registration',
                key: 'inviteCodeRequired',
                value: true,
                description: '是否需要邀请码注册'
            },
            {
                group: 'registration',
                key: 'inviteCode',
                value: 'COMPANY2024',
                description: '注册邀请码'
            },
            {
                group: 'registration',
                key: 'registrationEnabled',
                value: true,
                description: '是否开启注册功能'
            }
        ];
        // 逐个更新设置
        for (const setting of defaultSettings) {
            const [instance, created] = await Setting_1.default.findOrCreate({
                where: { group: setting.group, key: setting.key },
                defaults: setting
            });
            if (!created) {
                await instance.update(setting);
            }
        }
        // 获取更新后的设置
        const updatedSettings = await Setting_1.default.findAll();
        // 将设置转换为对象格式
        const settingsObject = updatedSettings.reduce((acc, setting) => {
            if (!acc[setting.group]) {
                acc[setting.group] = {};
            }
            acc[setting.group][setting.key] = setting.value;
            return acc;
        }, {});
        logger_1.default.info('系统设置初始化成功');
        res.json({
            code: 0,
            message: '默认设置初始化成功',
            data: {
                settings: settingsObject
            }
        });
    }
    catch (error) {
        logger_1.default.error('初始化设置失败:', error);
        res.status(500).json({
            code: 500,
            message: '初始化设置失败',
            error: error.message
        });
    }
};
exports.initializeSettings = initializeSettings;
