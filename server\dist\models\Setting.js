"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Setting = void 0;
const sequelize_1 = require("sequelize");
const db_1 = require("../config/db");
class Setting extends sequelize_1.Model {
}
exports.Setting = Setting;
Setting.init({
    id: {
        type: sequelize_1.DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    },
    group: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false
    },
    key: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false
    },
    value: {
        type: sequelize_1.DataTypes.JSON,
        allowNull: false
    },
    description: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true
    },
    created_at: {
        type: sequelize_1.DataTypes.DATE,
        defaultValue: sequelize_1.DataTypes.NOW
    },
    updated_at: {
        type: sequelize_1.DataTypes.DATE,
        defaultValue: sequelize_1.DataTypes.NOW
    }
}, {
    sequelize: db_1.sequelize,
    modelName: 'Setting',
    tableName: 'settings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
        {
            unique: true,
            fields: ['group', 'key']
        }
    ]
});
exports.default = Setting;
