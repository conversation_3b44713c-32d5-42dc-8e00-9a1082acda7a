"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const db_1 = require("../config/db");
// 导入所有模型以确保它们被注册
require("../models/User");
require("../models/Setting");
require("../models/Category");
require("../models/Tag");
require("../models/Announcement");
require("../models/Article");
require("../models/UserGroup");
require("../models/associations");
const models_1 = require("../models");
dotenv_1.default.config();
async function syncDatabase() {
    try {
        console.log('开始同步数据库...');
        // 测试数据库连接
        await db_1.sequelize.authenticate();
        console.log('数据库连接成功');
        // 设置模型关联关系
        (0, models_1.setupAssociations)();
        console.log('模型关联关系设置完成');
        // 同步数据库结构
        await db_1.sequelize.sync({ force: false, alter: true });
        console.log('数据库表结构同步完成');
        // 显示所有表
        const tables = await db_1.sequelize.getQueryInterface().showAllTables();
        console.log('当前数据库表:', tables);
        process.exit(0);
    }
    catch (error) {
        console.error('数据库同步失败:', error);
        process.exit(1);
    }
}
syncDatabase();
