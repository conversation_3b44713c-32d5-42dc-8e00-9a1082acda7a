<template>
  <div class="usergroups-container">
    <a-card class="mb-4">
      <template #title>
        <div class="card-header">
          <span>用户组管理</span>
          <a-button type="primary" @click="handleCreate" size="small">新建用户组</a-button>
        </div>
      </template>

      <a-table
        :loading="loading"
        :dataSource="userGroups"
        :columns="columns"
        :pagination="{
          total: userGroups.length,
          pageSize: 10,
          showTotal: total => `共 ${total} 条`
        }"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'members'">
            {{ record.members?.length || 0 }}
          </template>
          <template v-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="handleEdit(record)">编辑</a-button>
              <a-button type="link" @click="handleManageMembers(record)">管理成员</a-button>
              <a-popconfirm
                title="确定要删除此用户组吗？"
                @confirm="handleDelete(record._id || record.id)"
              >
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 用户组表单对话框 -->
    <a-modal
      :title="dialogType === 'create' ? '新建用户组' : '编辑用户组'"
      v-model:open="dialogVisible"
      @ok="submitForm"
      @cancel="dialogVisible = false"
      width="500px"
    >
      <a-form :model="form" :rules="rules" ref="formRef" layout="vertical">
        <a-form-item label="名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入用户组名称" />
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="form.description"
            :rows="4"
            placeholder="请输入用户组描述"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 成员管理对话框 -->
    <a-modal
      title="管理用户组成员"
      v-model:open="memberDialogVisible"
      width="700px"
      :footer="null"
    >
      <div v-if="currentGroup" class="mb-4">
        <h3>{{ currentGroup.name }}</h3>
        <p v-if="currentGroup.description">{{ currentGroup.description }}</p>
      </div>

      <div class="mb-4">
        <a-input-search
          v-model:value="userSearchKeyword"
          placeholder="搜索用户..."
          class="w-50 mr-4"
          @search="searchUsers"
          allowClear
        />
      </div>

      <div v-if="!isSelectingUsers" class="mb-4">
        <div class="flex justify-between items-center mb-2">
          <h4 class="m-0">当前成员 ({{ currentGroupMembers.length }})</h4>
          <a-button type="primary" size="small" @click="isSelectingUsers = true">添加成员</a-button>
        </div>
        <a-table
          :dataSource="currentGroupMembers"
          :columns="memberColumns"
          bordered
          :scroll="{ y: 300 }"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-popconfirm
                title="确定要移除此成员吗？"
                @confirm="handleRemoveUser(record._id)"
              >
                <a-button type="link" danger>移除</a-button>
              </a-popconfirm>
            </template>
          </template>
        </a-table>
      </div>

      <div v-else>
        <div class="flex justify-between items-center mb-2">
          <h4 class="m-0">选择要添加的用户 ({{ filteredUsers.length }})</h4>
          <a-button @click="isSelectingUsers = false">返回</a-button>
        </div>
        <a-table
          :dataSource="filteredUsers"
          :columns="userColumns"
          :rowSelection="{
            onChange: handleUserSelectionChange,
            selectedRowKeys: selectedRowKeys,
            columnWidth: '60px'
          }"
          :row-key="record => record._id"
          bordered
          :scroll="{ y: 300 }"
          :pagination="false"
        />
        <div class="mt-4 text-right">
          <a-button type="primary" @click="addSelectedUsersToGroup" :disabled="selectedUsers.length === 0">
            添加选中用户 ({{ selectedUsers.length }})
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import userGroupApi from '@/api/modules/userGroup';
import { userApi } from '@/api/modules/user';

interface UserGroup {
  _id: string;
  name: string;
  description?: string;
  members: string[];
  created_at: string;
  updated_at: string;
}

// 表格列定义
const columns = [
  {
    title: '用户组名称',
    dataIndex: 'name',
    key: 'name',
    width: 120
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
    width: 200
  },
  {
    title: '成员数量',
    key: 'members',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 220,
    fixed: 'right'
  }
];

const memberColumns = [
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username'
  },
  {
    title: '昵称',
    dataIndex: 'nickname',
    key: 'nickname'
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email'
  },
  {
    title: '操作',
    key: 'action',
    width: 120
  }
];

const userColumns = [
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username'
  },
  {
    title: '昵称',
    dataIndex: 'nickname',
    key: 'nickname'
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email'
  }
];

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleString();
};

// 状态变量
const loading = ref(false);
const removingMember = ref(false);
const userGroups = ref<UserGroup[]>([]);
const currentGroupMembers = ref<any[]>([]);
const userSearchResults = ref<any[]>([]);
const selectedUsers = ref<any[]>([]);
const selectedRowKeys = ref<string[]>([]);
const allUsers = ref<any[]>([]);
const filteredUsers = computed(() => {
  if (!userSearchKeyword.value) {
    return allUsers.value;
  }
  const keyword = userSearchKeyword.value.toLowerCase();
  return allUsers.value.filter(user =>
    user.username.toLowerCase().includes(keyword) ||
    user.email.toLowerCase().includes(keyword) ||
    (user.nickname && user.nickname.toLowerCase().includes(keyword))
  );
});

// 获取所有用户组
const fetchUserGroups = async () => {
  loading.value = true;
  try {
    const response = await userGroupApi.getUserGroups();
    console.log('用户组响应数据:', response);

    if (response.code === 0 || response.code === 200) {
      // 处理分页数据格式
      if (response.data && response.data.items) {
        userGroups.value = response.data.items || [];
      } else if (Array.isArray(response.data)) {
        userGroups.value = response.data;
      } else {
        userGroups.value = [];
      }
      console.log('处理后的用户组数据:', userGroups.value);
    } else {
      userGroups.value = [];
      console.warn('用户组数据格式不正确:', response);
      message.error(response.message || '获取用户组列表失败');
    }
  } catch (error: any) {
    console.error('获取用户组失败:', error);
    message.error(error.response?.data?.message || '获取用户组失败，请稍后重试');
    userGroups.value = [];
  } finally {
    loading.value = false;
  }
};

// 表单对话框
const dialogVisible = ref(false);
const dialogType = ref<'create' | 'edit'>('create');
const formRef = ref();
const form = reactive({
  _id: '',
  name: '',
  description: ''
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入用户组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
};

// 创建用户组
const handleCreate = () => {
  dialogType.value = 'create';
  form._id = '';
  form.name = '';
  form.description = '';
  dialogVisible.value = true;
};

// 编辑用户组
const handleEdit = (row: any) => {
  dialogType.value = 'edit';
  form._id = row._id;
  form.name = row.name;
  form.description = row.description || '';
  dialogVisible.value = true;
};

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value?.validate();
    if (dialogType.value === 'create') {
      await userGroupApi.createUserGroup({
        name: form.name,
        description: form.description
      });
      message.success('创建用户组成功');
    } else {
      await userGroupApi.updateUserGroup(form._id, {
        name: form.name,
        description: form.description
      });
      message.success('更新用户组成功');
    }
    dialogVisible.value = false;
    await fetchUserGroups();
  } catch (error) {
    console.error('提交用户组表单失败:', error);
    message.error(dialogType.value === 'create' ? '创建用户组失败' : '更新用户组失败');
  }
};

// 删除用户组
const handleDelete = async (id: string) => {
  try {
    const response = await userGroupApi.deleteUserGroup(id);
    if (response.code === 0) {
      message.success('删除用户组成功');
      await fetchUserGroups();
    } else {
      throw new Error(response.message || '删除失败');
    }
  } catch (error: any) {
    console.error('删除失败:', error);
    message.error(error.message || '删除失败，请稍后重试');
  }
};

// 成员管理相关
const memberDialogVisible = ref(false);
const currentGroup = ref<any>(null);
const isSelectingUsers = ref(false);
const userSearchKeyword = ref('');

// 打开成员管理对话框
const handleManageMembers = async (group: any) => {
  currentGroup.value = { ...group }; // 创建副本
  currentGroupMembers.value = Array.isArray(group.members) ? [...group.members] : []; // 创建副本
  memberDialogVisible.value = true;
  isSelectingUsers.value = false;
  userSearchKeyword.value = '';
  selectedUsers.value = [];
  selectedRowKeys.value = [];

  // 加载所有用户
  try {
    loading.value = true;
    const res = await userApi.getUsers({
      isAdmin: true,
      pageSize: 1000 // 获取足够多的用户
    });

    if (res.code === 0 || res.code === 200) {
      const memberIds = currentGroupMembers.value.map((m: any) => m._id);
      const items = Array.isArray(res.data?.items) ? res.data.items : [];
      // 过滤掉已经是成员的用户
      allUsers.value = items
        .filter((user: any) => !memberIds.includes(user._id))
        .map((user: any) => ({
          ...user,
          _id: user._id || user.id
        }));
    }
  } catch (error: any) {
    console.error('获取用户列表失败:', error);
    message.error(error.message || '获取用户列表失败');
    allUsers.value = [];
  } finally {
    loading.value = false;
  }
};

// 搜索用户 - 现在只需要更新搜索关键词,computed属性会自动过滤
const searchUsers = () => {
  userSearchResults.value = filteredUsers.value;
};

// 处理用户选择变化
const handleUserSelectionChange = (rowKeys: string[], selectedRows: any[]) => {
  console.log('选择变化 - rowKeys:', rowKeys);
  console.log('选择变化 - selectedRows:', selectedRows);
  selectedRowKeys.value = rowKeys;
  selectedUsers.value = selectedRows;
};

// 添加选中的用户到用户组
const addSelectedUsersToGroup = async () => {
  if (selectedUsers.value.length === 0) {
    message.warning('请选择要添加的用户');
    return;
  }

  try {
    loading.value = true;
    for (const user of selectedUsers.value) {
      await userGroupApi.addUserToGroup(currentGroup.value._id, user._id);
    }

    message.success(`成功添加 ${selectedUsers.value.length} 个用户到用户组`);

    // 重新获取用户组数据
    await fetchUserGroups();

    // 更新当前用户组成员列表
    const updatedGroup = userGroups.value.find((g: any) => g._id === currentGroup.value._id);
    if (updatedGroup) {
      currentGroup.value = { ...updatedGroup };
      currentGroupMembers.value = [...(updatedGroup.members || [])];
    }

    // 清空选择
    selectedUsers.value = [];
    selectedRowKeys.value = [];
    userSearchResults.value = [];
    userSearchKeyword.value = '';
    isSelectingUsers.value = false;
  } catch (error: any) {
    console.error('添加用户到用户组失败:', error);
    message.error(error.message || '添加用户到用户组失败');
  } finally {
    loading.value = false;
  }
};

// 从用户组移除用户
const handleRemoveUser = async (userId: string) => {
  try {
    removingMember.value = true;
    const response = await userGroupApi.removeUserFromGroup(currentGroup.value._id, userId);

    if (response.code === 0) {
      message.success('移除成员成功');

      // 更新当前组成员列表
      currentGroupMembers.value = currentGroupMembers.value.filter(member => member._id !== userId);

      // 更新用户组列表中的数据
      const groupIndex = userGroups.value.findIndex(g => g._id === currentGroup.value._id);
      if (groupIndex > -1) {
        userGroups.value[groupIndex].members = userGroups.value[groupIndex].members.filter(
          member => member._id !== userId
        );
        // 更新当前组信息
        currentGroup.value = { ...userGroups.value[groupIndex] };
      }
    } else {
      throw new Error(response.message || '移除成员失败');
    }
  } catch (error: any) {
    console.error('移除成员失败:', error);
    message.error(error.message || '移除成员失败，请稍后重试');
  } finally {
    removingMember.value = false;
  }
};

onMounted(() => {
  fetchUserGroups();
});
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mr-4 {
  margin-right: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.m-0 {
  margin: 0;
}

.w-50 {
  width: 50%;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.text-right {
  text-align: right;
}
</style>
